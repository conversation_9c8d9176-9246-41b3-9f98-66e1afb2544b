<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.fragments.SavedListSearchFragment">

    <LinearLayout
        android:id="@+id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/space_x1"

        android:background="@color/white"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/space_x2"
        android:paddingTop="@dimen/space_x2">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:weightSum="1">

            <TextView
                style="@style/BodyMediumText.Bold.Black"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/space_x1"
                android:layout_weight="1"
                android:text="@string/daftar_tersimpan" />

            <LinearLayout
                android:id="@+id/ll_add_saved_list"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/space_half"
                android:visibility="visible">

                <TextView
                    style="@style/Body3SmallText.Bold.PrimaryBlue80"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/txt_tambah" />
            </LinearLayout>
        </LinearLayout>

        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchview_briva"
            style="@style/Body3SmallText.Medium.NeutralLight60"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x2"
            android:layout_marginBottom="@dimen/space_x2"
            android:background="@drawable/bg_grey_border"
            android:paddingVertical="@dimen/space_x2"
            android:paddingStart="@dimen/space_minx1_half"
            android:paddingEnd="@dimen/space_x1_half"
            app:closeIcon="@drawable/ic_cancel_20_neutrallight80"
            app:iconifiedByDefault="false"
            app:queryBackground="@color/transparent"
            app:queryHint="@string/transfer_search_query_hint_brizzi"
            app:searchIcon="@drawable/ic_search_outline_16dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_daftar_briva"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layoutAnimation="@anim/layout_animation_fade_in"
            android:overScrollMode="never" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/ll_no_data_saved"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_x1_half"
                    android:gravity="top"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/space_x16"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/ilustrasi_no_saved_box" />

                    <TextView
                        style="@style/Body2MediumText.SemiBold.NeutralDark40"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1_half"
                        android:text="@string/no_favorit"
                        android:textAlignment="center" />

                    <TextView
                        style="@style/Body3SmallText.Medium.NeutralLight80"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_half"
                        android:text="@string/no_favorit_desc_brizzi"
                        android:textAlignment="center" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</FrameLayout>