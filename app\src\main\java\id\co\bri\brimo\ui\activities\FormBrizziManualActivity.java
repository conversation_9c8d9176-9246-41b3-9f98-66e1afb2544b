package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.text.Editable;
import android.util.Log;
import android.view.View;
import android.widget.Button;
//import android.widget.CardView;
import androidx.cardview.widget.CardView;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HistoryAdapter;
import id.co.bri.brimo.adapters.option.OptionNominalAdapter;
import id.co.bri.brimo.contract.IPresenter.brizzi.IFormBrizziManualPresenter;
import id.co.bri.brimo.contract.IView.brizzi.IFormBrizziManualView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.SizeHelper;
import id.co.bri.brimo.models.Amount;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.BrizziResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.optionmodel.OptionNominalModel;
import id.co.bri.brimo.ui.activities.base.BaseFormNosavedActivity;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import butterknife.Bind;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import io.rmiri.skeleton.SkeletonViewGroup;

public class FormBrizziManualActivity extends BaseFormNosavedActivity implements
        IFormBrizziManualView,
        View.OnClickListener,
        SkeletonViewGroup.SkeletonListener,
        OptionNominalAdapter.onAddButtonListener,
        HistoryAdapter.ClickItem,
        SwipeRefreshLayout.OnRefreshListener, DialogExitCustom.DialogDefaultListener {

    @Bind(R.id.etBrizzi)
    EditText editText;
    @Bind(R.id.rlKeterangan)
    RelativeLayout relativeLayout;
    @Bind(R.id.tv_total_pembayaran_pulsa)
    TextView totalpembayaran;
    @Bind(R.id.cv_brizzi)
    CardView cvBrizzi;
    @Bind(R.id.content)
    CoordinatorLayout coordinatorLayout;
    @Bind(R.id.rv_nominal_brizzi)
    RecyclerView recyclerView;
    @Bind(R.id.ll_brizzi)
    LinearLayout linearLayout;
    @Bind(R.id.btnSubmitBrizzi)
    Button btnSubmitBrizzi;
    @Bind(R.id.viewBrizii)
    View view3;
    @Bind(R.id.viewBriziiPanjang)
    View view4;
    @Bind(R.id.tvnominal)
    TextView tvnominal;

    static String errorMessage = null;
    private ArrayList<OptionNominalModel> nominallList = new ArrayList<>();
    private int currentHarga, currentPosition;
    private OptionNominalAdapter optionNominalAdapter;
    private List<Amount> nominalList;
    StringBuilder stringBuilder;
    private String amountPay = "0";
    static Integer mState;
    
    private static final int TOTAL_SYMBOLS = 19; // size of pattern 0000-0000-0000-0000
    private static final int TOTAL_DIGITS = 16; // max numbers of digits in pattern: 0000 x 4
    private static final int DIVIDER_MODULO = 5; // means divider position is every 5th symbol beginning with 1
    private static final int DIVIDER_POSITION = DIVIDER_MODULO - 1; // means divider position is every 4th symbol beginning with 0
    private static final char DIVIDER = ' ';

    @Inject
    IFormBrizziManualPresenter<IFormBrizziManualView> brizziManualPresenter;

    public static void launchIntent(Activity caller, boolean isFromFast) {
        Intent intent = new Intent(caller, FormBrizziManualActivity.class);
        intent.putExtra(Constant.TAG_FROM_FASTMENU, isFromFast);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        btnSubmitBrizzi.setOnClickListener(this);
        
        view3.setVisibility(View.VISIBLE);
        view4.setVisibility(View.GONE);
        SizeHelper.setMarginsView(this, view3, 9, 0, 0, 0);

        editText.addTextChangedListener(activityTextListener);
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (brizziManualPresenter != null) {
            initiateURLPresenter();

            if (isFromFastMenu)
                brizziManualPresenter.getDataFormFastMenuFinishActivity();
            else
                brizziManualPresenter.getDataForm();
        }
    }

    @Override
    public int getLayoutResource() {
        return R.layout.activity_form_brizzi_manual;
    }

    @Override
    public int getDefaultIconResource() {
        return R.drawable.brizzi;
    }

    @Override
    public void setTextForm() {
        btnSubmitBrizzi.setText(GeneralHelper.getString(R.string.buy));
    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.brizzi_titlebar);
    }

    @Override
    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.payment));
        parameterModel.setDefaultIcon(getDefaultIconResource());

        return parameterModel;
    }

    private void initiateURLPresenter() {
        brizziManualPresenter.setView(this);
        brizziManualPresenter.start();
        if (isFromFastMenu) {
            brizziManualPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_brizzi_fm));
            brizziManualPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_brizzi_fm));
            brizziManualPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_brizzi_fm));
            brizziManualPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_brizzi_fm));
        } else {
            brizziManualPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_brizzi));
            brizziManualPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_brizzi));
            brizziManualPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_brizzi));
            brizziManualPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_brizzi));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (brizziManualPresenter != null) {
            initiateURLPresenter();
        }
    }

    @Override
    protected void changeText(CharSequence charSequence, int i, int i1, int i2) {
        super.changeText(charSequence, i, i1, i2);
        if (editText.length() > 0) {
            disableButtonSubmit(true);
            relativeLayout.setVisibility(View.VISIBLE);
            linearLayout.setVisibility(View.GONE);
            onAnimator(cvBrizzi, false, ANIMATE_GONE, Constant.REQUEST_BRIZZI);
        }

        if (editText.length() >= 19) {
            relativeLayout.setVisibility(View.GONE);
            linearLayout.setVisibility(View.VISIBLE);
            setupView();
            disableButtonSubmit(false);
        } else disableButtonSubmit(true);
    }

    @Override
    protected void afterText(Editable editable) {
        super.afterText(editable);
        if (!isInputCorrect(editable, TOTAL_SYMBOLS, DIVIDER_MODULO, DIVIDER)) {
            editable.replace(0, editable.length(), buildCorrectString(getDigitArray(editable, TOTAL_DIGITS), DIVIDER_POSITION, DIVIDER));
        }
    }

    @Override
    public void callback(int harga, int position) {
        currentHarga = harga;
        currentPosition = position;
        amountPay = String.valueOf(harga);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            nominallList.forEach((p) -> p.setBol(true));
        } else {
            for (OptionNominalModel p : nominallList) {
                p.setBol(true);
            }
        }

        nominallList.get(position).setBol(false);
        optionNominalAdapter.notifyDataSetChanged();
        totalpembayaran.setText(("Rp" + GeneralHelper.formatNominal(String.valueOf(harga)) + ""));
        btnSubmitBrizzi.setEnabled(true);

        onAnimator(cvBrizzi, true, ANIMATE_SHOW, Constant.REQUEST_BRIZZI);
        SizeHelper.setMarginsView(this, coordinatorLayout, 0, 0, 0, 75);
    }

    private ArrayList<OptionNominalModel> parseListNominal(List<Amount> amountOption) {
        ArrayList<OptionNominalModel> nominal = new ArrayList<>();

        if (amountOption != null) {
            for (int i = 0; i < amountOption.size(); i++) {
                nominal.add(new OptionNominalModel(i, amountOption.get(i).getValue(), amountOption.get(i).getText(), true));
            }
        }

        return nominal;
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    protected void setupView() {
        //parse data list Nominal
        nominallList = parseListNominal(nominalList);
        recyclerView.setLayoutManager(new GridLayoutManager(getApplicationContext(), 2, RecyclerView.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        optionNominalAdapter = new OptionNominalAdapter(nominallList, getApplicationContext(), this);
        optionNominalAdapter.notifyItemRangeChanged(0, nominallList.size());
        recyclerView.setAdapter(optionNominalAdapter);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                //membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (data != null) {
                    //membalikan data error message agar muncul snackbar di dashboard
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    if (errorMessage != null){
                        showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);
                    }
                    final Handler handler = new Handler();
                    handler.postDelayed(() -> {
                        Log.d("313", data.getStringExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE)+"dasd");
                        String errorMsgValidate = data.getStringExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE);
                        if (errorMsgValidate != null){
                            showSnackbarErrorMessage(errorMsgValidate, ALERT_ERROR, this, false);
                        }
                    }, 2000);
                }
            }
        }
    }

    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        if (id == R.id.btnSubmitBrizzi) {
            onAddNewClick();
        }
    }

    @Override
    public void onRefresh() {

    }

    @Override
    public void onClickHistoryItem(HistoryResponse historyResponse) {
        stringBuilder = new StringBuilder(historyResponse.getSubtitle());

        for (int i = 4; i < stringBuilder.length(); i += 5) {
            stringBuilder.insert(i, " ");
        }
        editText.setText(stringBuilder.toString());
    }

    @Override
    public void onStartAnimation() {

    }

    @Override
    public void onFinishAnimation() {

    }

    /**
     * Method untuk format credit card 4 spasi di Edit Text
     */
    private boolean isInputCorrect(Editable s, int totalSymbols, int dividerModulo, char divider) {
        boolean isCorrect = s.length() <= totalSymbols; // check size of entered string
        for (int i = 0; i < s.length(); i++) { // check that every element is right
            if (i > 0 && (i + 1) % dividerModulo == 0) {
                isCorrect &= divider == s.charAt(i);
            } else {
                isCorrect &= Character.isDigit(s.charAt(i));
            }
        }
        return isCorrect;
    }

    private String buildCorrectString(char[] digits, int dividerPosition, char divider) {
        final StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < digits.length; i++) {
            if (digits[i] != 0) {
                formatted.append(digits[i]);
                if ((i > 0) && (i < (digits.length - 1)) && (((i + 1) % dividerPosition) == 0)) {
                    formatted.append(divider);
                }
            }
        }

        return formatted.toString();
    }

    private char[] getDigitArray(final Editable s, final int size) {
        char[] digits = new char[size];
        int index = 0;
        for (int i = 0; i < s.length() && index < size; i++) {
            char current = s.charAt(i);
            if (Character.isDigit(current)) {
                digits[index] = current;
                index++;
            }
        }
        return digits;
    }

    @Override
    public void onSuccessGetRestResponse(RestResponse restResponse) {
        BrizziResponse brizziResponse = restResponse.getData(BrizziResponse.class);
        nominalList = brizziResponse.getAmount();
    }

    @Override
    public void onAddNewClick() {
        String str = editText.getText().toString().replace(" ", "");
        brizziManualPresenter.getDataInquiry(str, String.valueOf(nominallList.get(currentPosition).getHarga()), isFromFastMenu);
    }

    @Override
    public void onSuccessGetInquiryDeposito(GeneralInquiryResponse kaiInquiryResponse, String konfirmasiUrl, String paymentUrl) {
        InquiryPlnTokenActivity.launchIntent(
                this,
                kaiInquiryResponse,
                getTitleBar(),
                konfirmasiUrl,
                paymentUrl,
                setParameter(),
                isFromFastMenu
        );
    }

    protected void disableButtonSubmit(boolean disable) {
        if (disable) {
            btnSubmitBrizzi.setEnabled(false);
            btnSubmitBrizzi.setAlpha((float) 0.3);
        } else {
            btnSubmitBrizzi.setEnabled(true);
            btnSubmitBrizzi.setAlpha(1);
        }
    }

    @Override
    protected void onDestroy() {
        brizziManualPresenter.stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        brizziManualPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onClickYes() {

    }
}
