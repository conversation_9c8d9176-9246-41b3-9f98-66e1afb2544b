<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <include
        android:id="@+id/tb_briva"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_briva">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:cardElevation="@dimen/_5sdp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_below="@id/kontak_terakhir_briva"
                        android:layout_marginStart="@dimen/_15sdp"
                        android:layout_marginTop="@dimen/_11sdp"
                        android:layout_marginEnd="@dimen/_15sdp"
                        android:orientation="vertical">


                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_5sdp"
                            android:layout_marginBottom="@dimen/_8sdp"
                            android:fontFamily="@font/avenir_next_demi"
                            android:text="@string/widget_brizzi"
                            android:textColor="@color/white"
                            android:textSize="@dimen/size_saved_subtitle_trx" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="10dp"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:paddingEnd="25dp"
                                tools:ignore="RtlSymmetry">

                                <TextView
                                    android:layout_width="287dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="5.5dp"
                                    android:fontFamily="@font/avenir_next_medium"
                                    android:text="@string/scan_kartu"
                                    android:textColor="@color/colorTextMenuBottomNavigation"
                                    android:textSize="@dimen/size_default_13sp" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/cek_update_saldo" />

                            </LinearLayout>

                            <Button
                                android:id="@+id/btnCekSaldo"
                                android:layout_width="80dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:layout_marginStart="-80dp"
                                android:background="@drawable/rounded_button_blue_brizzi"
                                android:fontFamily="@font/avenir_next_bold"
                                android:text="Scan"
                                android:textAllCaps="false"
                                android:textColor="@android:color/white"
                                android:textSize="14dp" />
                        </LinearLayout>



                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="5dp"
                    android:background="@color/white" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/cvkontakbriva"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:id="@+id/kontak_terakhir_briva"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingBottom="3dp">

                        <TextView
                            android:id="@+id/tv_last_trx"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_20sdp"
                            android:layout_marginTop="@dimen/margin_from_top_layout"
                            android:fontFamily="@font/avenir_next_bold"
                            android:text="@string/last_transaction"
                            android:textColor="@color/colorMenuBottomNavigation"
                            android:textSize="@dimen/size_konfirmasi_subtitle" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_briva_terakhir"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clipToPadding="false"
                            android:layoutAnimation="@anim/layout_animation_fade_in"
                            android:paddingStart="@dimen/_12sdp"
                            android:paddingRight="@dimen/_25sdp" />

                        <include
                            android:id="@+id/layout_no_data"
                            layout="@layout/item_no_data_transaction"
                            android:visibility="gone" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <Button
            android:id="@+id/btn_masukkan_no_kartu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="@dimen/_15sdp"
            android:layout_marginTop="@dimen/_11sdp"
            android:layout_marginEnd="@dimen/_15sdp"
            android:layout_marginBottom="@dimen/_15sdp"
            android:background="@drawable/rounded_button_blue_brizzi_card"
            android:fontFamily="@font/avenir_next_bold"
            android:text="@string/masukkan_no_kartu_brizzi"
            android:textColor="@color/white" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <RelativeLayout
        android:id="@+id/rlKeterangan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="20dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/rounded_line_blue"
        android:outlineProvider="bounds"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorButtonGrey"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="9dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="7dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@drawable/alert_brizzi"
                    android:textColor="@color/primaryColor" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="30dp"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="3dp"
                    android:fontFamily="@font/avenir_next_bold"
                    android:gravity="center_horizontal"
                    android:text="@string/txt_description"
                    android:textColor="@color/primaryColor"
                    android:textSize="11sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="7dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:fontFamily="@font/avenir_next_medium"
                    android:text="@string/_1"
                    android:textColor="@color/black20"
                    android:textSize="@dimen/size_default_12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="7dp"
                    android:fontFamily="@font/avenir_next_medium"
                    android:text="@string/brizzi_keterangan_1"
                    android:textColor="@color/black20"
                    android:textSize="@dimen/size_default_12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:layout_marginBottom="15dp"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:fontFamily="@font/avenir_next_medium"
                    android:text="@string/_2"
                    android:textColor="@color/black20"
                    android:textSize="@dimen/size_default_12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginEnd="7dp"
                    android:fontFamily="@font/avenir_next_medium"
                    android:text="@string/brizzi_keterangan_2"
                    android:textColor="@color/black20"
                    android:textSize="@dimen/size_default_12sp" />

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>



</RelativeLayout>