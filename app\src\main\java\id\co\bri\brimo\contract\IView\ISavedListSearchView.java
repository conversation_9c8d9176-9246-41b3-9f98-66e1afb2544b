package id.co.bri.brimo.contract.IView;

import java.util.List;

import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.models.apimodel.response.FilterAktivityResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.InboxResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimo.models.apimodel.response.PendingResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptAmkkmResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptInternasionalResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptResponse;
import id.co.bri.brimo.models.apimodel.response.ReceiptTravelTrainResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;

public interface ISavedListSearchView extends IMvpView {

    String getTitleBar();

    void isHideSkeleton(boolean hide);

    void onSuccessGetHistoryForm(List<HistoryResponse> historyResponses);

    void onSuccessGetRestResponse(RestResponse restResponse);

    void onSuccessGetSavedForm(List<SavedResponse> savedResponses);

    void onSuccessGetInquiry(InquiryTransferRevampResponse inquiryTransferRevampResponse);

    void onSuccessUpdate(SavedResponse savedResponse, int item, int type);

    void onShowBubbleFirstTimeVisit();
}
