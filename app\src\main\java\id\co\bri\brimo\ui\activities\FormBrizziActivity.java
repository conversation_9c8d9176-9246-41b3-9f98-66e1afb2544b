package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.nfc.NfcAdapter;
import android.nfc.NfcManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.Editable;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.cardview.widget.CardView;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HistoryAdapter;
import id.co.bri.brimo.adapters.option.OptionNominalAdapter;
import id.co.bri.brimo.contract.IPresenter.brizzi.IFormBrizziPresenter;
import id.co.bri.brimo.contract.IView.brizzi.IFormBrizziView;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.SizeHelper;
import id.co.bri.brimo.models.Amount;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.response.BrizziResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.optionmodel.OptionNominalModel;
import id.co.bri.brimo.ui.activities.base.BaseFormNosavedActivity;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;

import butterknife.Bind;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom;
import io.rmiri.skeleton.SkeletonViewGroup;

public class FormBrizziActivity extends BaseFormNosavedActivity implements
        IFormBrizziView,
        View.OnClickListener,
        SkeletonViewGroup.SkeletonListener,
        HistoryAdapter.ClickItem,
        SwipeRefreshLayout.OnRefreshListener, DialogExitCustom.DialogDefaultListener {

    @Bind(R.id.btnCekSaldo)
    Button btnCekSaldo;
    @Bind(R.id.btn_masukkan_no_kartu)
    Button btnMasukkanNoKartu;
    static String errorMessage = null;
    NfcManager manager;
    NfcAdapter adapter;

    @Inject
    IFormBrizziPresenter<IFormBrizziView> brizziPresenter;

    public static void launchIntent(Activity caller, boolean isFromFast) {
        Intent intent = new Intent(caller, FormBrizziActivity.class);
        intent.putExtra(Constant.TAG_FROM_FASTMENU, isFromFast);
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        btnCekSaldo.setOnClickListener(this);
        btnMasukkanNoKartu.setOnClickListener(this);

        manager = (NfcManager) getApplicationContext().getSystemService(Context.NFC_SERVICE);
        adapter = manager.getDefaultAdapter();
        if (adapter != null) {

            if (adapter.isEnabled()) {
                btnCekSaldo.setVisibility(View.VISIBLE);
            } else {
                AlertDialog.Builder alertbox = new AlertDialog.Builder(this);
                alertbox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut));
                alertbox.setPositiveButton(GeneralHelper.getString(R.string.aktifkan2), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        {
                            Intent intent = new Intent(Settings.ACTION_NFC_SETTINGS);
                            startActivity(intent);
                        }
                    }
                });
                alertbox.setNegativeButton(GeneralHelper.getString(R.string.batal), new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                    }
                });
                alertbox.show();
            }

        } else {
            GeneralHelper.showBottomDialog(this, Constant.NFC_GAGAL);
            btnCekSaldo.setVisibility(View.GONE);
        }

    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (brizziPresenter != null) {
            initiateURLPresenter();

            if (isFromFastMenu)
                brizziPresenter.getDataFormFastMenuFinishActivity();
            else
                brizziPresenter.getDataForm();
        }
    }

    @Override
    public int getLayoutResource() {
        return R.layout.activity_form_brizzi;
    }

    @Override
    public int getDefaultIconResource() {
        return R.drawable.brizzi;
    }

//    @Override
//    public void setTextForm() {
//        btnSubmitBrizzi.setText(GeneralHelper.getString(R.string.buy));
//    }

    @Override
    public void setTextForm() {
        // Text form setup for NFC functionality - manual input moved to FormBrizziManualActivity
    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.brizzi_titlebar);
    }

    @Override
    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.nomor_tujuan));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.nominal));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.lanjutkan));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.payment));
        parameterModel.setDefaultIcon(getDefaultIconResource());

        return parameterModel;
    }

    private void initiateURLPresenter() {
        brizziPresenter.setView(this);
        brizziPresenter.start();
        if (isFromFastMenu) {
            brizziPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_brizzi_fm));
            brizziPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_brizzi_fm));
            brizziPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_brizzi_fm));
            brizziPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_brizzi_fm));
        } else {
            brizziPresenter.setFormUrl(GeneralHelper.getString(R.string.url_form_brizzi));
            brizziPresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_inquiry_brizzi));
            brizziPresenter.setKonfirmasiUrl(GeneralHelper.getString(R.string.url_konfirmasi_brizzi));
            brizziPresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_brizzi));
        }

    }



    @Override
    protected void onResume() {
        super.onResume();
        if (brizziPresenter != null) {
            initiateURLPresenter();
        }
    }





    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }



    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                //membalikan data agar otomatis cek saldo
                setResult(RESULT_OK, data);
                this.finish();
            } else {
                if (data != null) {
                    //membalikan data error message agar muncul snackbar di dashboard
                    errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE);
                    if (errorMessage != null){
                        showSnackbarErrorMessage(errorMessage, ALERT_ERROR, this, false);
                    }
                    final Handler handler = new Handler();
                    handler.postDelayed(() -> {
                        Log.d("313", data.getStringExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE)+"dasd");
                        String errorMsgValidate = data.getStringExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE);
                        if (errorMsgValidate != null){
                            showSnackbarErrorMessage(errorMsgValidate, ALERT_ERROR, this, false);
                        }
                    }, 2000);
                }
            }
        }
    }


    @Override
    public void onClick(View view) {
        if (SystemClock.elapsedRealtime() - mLastClickTime < 1000) {
            return;
        }
        mLastClickTime = SystemClock.elapsedRealtime();
        int id = view.getId();
        switch (id) {
            case R.id.btnCekSaldo:
                if (!adapter.isEnabled()) {
                    AlertDialog.Builder alertbox = new AlertDialog.Builder(this);
                    alertbox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut));
                    alertbox.setPositiveButton(GeneralHelper.getString(R.string.aktifkan2), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                Intent intent = new Intent(Settings.ACTION_NFC_SETTINGS);
                                startActivity(intent);
                            } else {
                                Intent intent = new Intent(Settings.ACTION_WIRELESS_SETTINGS);
                                startActivity(intent);
                            }
                        }
                    });
                    alertbox.setNegativeButton(GeneralHelper.getString(R.string.batal), new DialogInterface.OnClickListener() {

                        @Override
                        public void onClick(DialogInterface dialog, int which) {

                        }
                    });
                    alertbox.show();
                } else
                    CekBrizziSatuActivity.launchIntent(this, isFromFastMenu);
                break;
            case R.id.btn_masukkan_no_kartu:
                FormBrizziManualActivity.launchIntent(this, isFromFastMenu);
                break;
        }
    }

    @Override
    public void onRefresh() {

    }

    @Override
    public void onClickHistoryItem(HistoryResponse historyResponse) {
        // Redirect to manual activity with pre-filled data
        FormBrizziManualActivity.launchIntent(this, isFromFastMenu);
    }


    @Override
    public void onStartAnimation() {

    }

    @Override
    public void onFinishAnimation() {

    }



    @Override
    public void onSuccessGetRestResponse(RestResponse restResponse) {
        // Data will be handled in FormBrizziManualActivity
    }

    @Override
    public void onAddNewClick() {
        // Manual input functionality moved to FormBrizziManualActivity
    }

    @Override
    public void onSuccessGetInquiryDeposito(GeneralInquiryResponse kaiInquiryResponse, String konfirmasiUrl, String paymentUrl) {
        // Manual input functionality moved to FormBrizziManualActivity
    }

    @Override
    public void cekBrizziDariLuar() {
        CekBrizziDariLuarActivity.launchIntent(this, false);
    }


    @Override
    public void onSessionEnd(String message) {
        if (isFromFastMenu)
            CekBrizziDariLuarActivity.launchIntent(this, false);
        else
            super.onSessionEnd(message);

    }



    @Override
    protected void onDestroy() {
        brizziPresenter.stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        brizziPresenter.stop();
        super.onBackPressed();
    }

    @Override
    public void onClickYes() {

    }
}