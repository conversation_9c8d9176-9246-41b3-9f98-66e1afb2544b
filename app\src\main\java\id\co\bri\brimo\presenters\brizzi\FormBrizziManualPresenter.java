package id.co.bri.brimo.presenters.brizzi;

import id.co.bri.brimo.contract.IPresenter.brizzi.IFormBrizziManualPresenter;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.contract.IView.base.IBaseFormView;
import id.co.bri.brimo.contract.IView.brizzi.IFormBrizziManualView;
import id.co.bri.brimo.data.api.observer.ApiObserver;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.models.apimodel.request.FastInquiryBrizziRequest;
import id.co.bri.brimo.models.apimodel.request.InquiryDepositBrizziRequest;
import id.co.bri.brimo.models.apimodel.response.GeneralInquiryResponse;
import id.co.bri.brimo.presenters.base.BaseFormPresenter;

import io.reactivex.disposables.CompositeDisposable;

public class FormBrizziManualPresenter<V extends IMvpView & IBaseFormView & IFormBrizziManualView> extends BaseFormPresenter<V> implements IFormBrizziManualPresenter<V> {

    private static final String TAG = "FormBrizziManualPresenter";

    public FormBrizziManualPresenter(SchedulerProvider schedulerProvider, CompositeDisposable compositeDisposable, BRImoPrefSource mBRImoPrefRepository, ApiSource apiSource, CategoryPfmSource categoryPfmSource, TransaksiPfmSource transaksiPfmSource, AnggaranPfmSource anggaranPfmSource) {
        super(schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, categoryPfmSource, transaksiPfmSource, anggaranPfmSource);
    }

    /**
     * @param card
     * @param amount
     * @param isFormFast
     */
    @Override
    public void getDataInquiry(String card, String amount, boolean isFormFast) {
        if (isViewAttached()) {
            getView().showProgress();

            if (isFormFast)
                inquiryRequest = new FastInquiryBrizziRequest(getFastMenuRequest(), card, amount);
            else
                inquiryRequest = new InquiryDepositBrizziRequest(card, amount);

            String seqNum = getBRImoPrefRepository().getSeqNumber();

            getCompositeDisposable().add(getApiSource()
                    .getInquiryBrizzi(inquiryRequest, seqNum)
                    .subscribeOn(getSchedulerProvider().io())
                    .observeOn(getSchedulerProvider().ui())
                    .subscribeWith(new ApiObserver<GeneralInquiryResponse>() {
                        @Override
                        public void onNext(GeneralInquiryResponse generalInquiryResponse) {
                            super.onNext(generalInquiryResponse);
                            if (isViewAttached()) {
                                getView().onSuccessGetInquiryDeposito(generalInquiryResponse, getKonfirmasiUrl(), getPaymentUrl());
                            }
                        }
                    }));
        }
    }
}
