package id.co.bri.brimo.di.components;

import android.content.Context;

import dagger.Component;
import id.co.bri.brimo.contract.IPresenter.pengelolaankartu.IChangeDebitPINPresenter;
import id.co.bri.brimo.contract.IView.pengelolaankartu.IChangeDebitPINView;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource;
import id.co.bri.brimo.data.repository.category.CategoryPfmSource;
import id.co.bri.brimo.data.repository.fastmenu.FastMenuSource;
import id.co.bri.brimo.data.repository.fileparameter.FileParameterSource;
import id.co.bri.brimo.data.repository.lifestyle.MenuLifestyleSource;
import id.co.bri.brimo.data.repository.menudashall.MenuDashAllSource;
import id.co.bri.brimo.data.repository.menudashfav.MenuDashFavSource;
import id.co.bri.brimo.data.repository.rate.RateSource;
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource;
import id.co.bri.brimo.di.components.subcomponent.PinAllComponent;
import id.co.bri.brimo.di.modules.ActivityModule;
import id.co.bri.brimo.di.modules.fragment.PinAllModule;
import id.co.bri.brimo.di.scopes.PerActivity;
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider;
import id.co.bri.brimo.featurex.miniapp.view.ListAccountPalmActivity;
import id.co.bri.brimo.featurex.miniapp.view.ListMiniAppActivity;
import id.co.bri.brimo.featurex.miniapp.view.*;
import id.co.bri.brimo.ui.activities.nfcqrtap.NfcPaymentActivity;
import id.co.bri.brimo.ui.activities.*;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccConfirmActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccInfoActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccInformationAddressActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccInformationJobActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccInformationPersonalActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccOtpActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccPhotoKtpActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccPhotoSelfieActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccProductDetailActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccProductListActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccSofListActivity;
import id.co.bri.brimo.ui.activities.asuransi.InquiryMicrositeBrivaActivity;
import id.co.bri.brimo.ui.activities.asuransi.JenisTransaksiAsuransiActivity;
import id.co.bri.brimo.ui.activities.asuransi.ListProdukAsuransiActivity;
import id.co.bri.brimo.ui.activities.asuransi.MicrositeActivity;
import id.co.bri.brimo.ui.activities.cc_sof.*;
import id.co.bri.brimo.ui.activities.birthdayonboarding.BirthdayOnboardingActivity;
import id.co.bri.brimo.ui.activities.ccqrismpm.SofQrisActivity;
import id.co.bri.brimo.ui.activities.dplk.InfoDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.BrifineKombinasiRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DashboardDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DetailBrifineDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DetailProductFtuDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FirstTimeDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FormDataIuranDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FormPilihBrifineRevampActivity;
import id.co.bri.brimo.ui.activities.dashboardInvestasi.DashboardInvestasiActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FormTopupDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.InquiryAutoPaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.InquiryDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.KonfirmasiInquiryGeneralActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.ListDplkOptionRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.OnboardingAutopaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.ReceiptPendingRegistDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.StepPickProfileRiskDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.StepPersonalDataDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.RiwayatSetoranBrifineDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.SettingAutoPaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.SimulasiBrifineDplkActivity;
import id.co.bri.brimo.ui.activities.emas.FormEditSavedEmasActivity;
import id.co.bri.brimo.ui.activities.emas.InquiryCetakEmasActivity;
import id.co.bri.brimo.ui.activities.emas.StatusAmbilFisikActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.TambahDaftarDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.TermsAndConditionFtuDplkActivity;
import id.co.bri.brimo.ui.activities.finansialrek.InputDataFinansialActivity;
import id.co.bri.brimo.ui.activities.finansialrek.PerekamanVideoFinansialActivity;
import id.co.bri.brimo.ui.activities.finansialrek.WaitingFinansialActivity;
import id.co.bri.brimo.ui.activities.asuransi.MicrositeLamaActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.DashboardAsuransiActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.DetailAsuransiRevampActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.DetailBeliAsuransiActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.FormAsuransiRevampActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.ListAsuransiActivity;
import id.co.bri.brimo.ui.activities.autograbfund.DashboardAutoGrabFundActivity;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.base.BaseFormActivity;
import id.co.bri.brimo.ui.activities.base.BaseFormNosavedActivity;
import id.co.bri.brimo.ui.activities.base.BaseFormRevampActivity;
import id.co.bri.brimo.ui.activities.base.BaseInquiryActivity;
import id.co.bri.brimo.ui.activities.base.BaseInquiryRevampActivity;
import id.co.bri.brimo.ui.activities.birthdayonboarding.BirthdayOnboardingActivity;
import id.co.bri.brimo.ui.activities.bripoin.BripoinActivity;
import id.co.bri.brimo.ui.activities.bripoin.DetailAkunActivity;
import id.co.bri.brimo.ui.activities.britamajunio.DetailRekeningJunioActivity;
import id.co.bri.brimo.ui.activities.britamajunio.FormBritamaJunioActivity;
import id.co.bri.brimo.ui.activities.britamajunio.InquiryOpenJunioActivity;
import id.co.bri.brimo.ui.activities.britamajunio.KonfirmasiOpenJunioActivity;
import id.co.bri.brimo.ui.activities.britamajunio.PilihKantorJunioActivity;
import id.co.bri.brimo.ui.activities.britamarencana.DetailRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.HitungRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.KonfirmasiDataRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.KonfirmasiRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.PencairanRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencana.PilihJenisTabunganActivity;
import id.co.bri.brimo.ui.activities.britamarencana.PilihRekeningActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.DashboardRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.DetailRencanaRevampActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.EditTargetMoActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.FirstTimeRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.InquiryPencairanRencanaActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.KonfirmasiRencanaRevampActivity;
import id.co.bri.brimo.ui.activities.general.GeneralSNKWithPinActivity;
import id.co.bri.brimo.ui.activities.general.GeneralSNKwithChecklistActivity;
import id.co.bri.brimo.ui.activities.general.ReceiptStatusActivity;
import id.co.bri.brimo.ui.activities.ibbiz.IbbizActivity;
import id.co.bri.brimo.ui.activities.notification.NotificationSettingActivity;
import id.co.bri.brimo.ui.activities.notification.NotificationSettingDetailActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingCameraActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingCheckPointActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingConfirmPinActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataAlamatActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataKeuanganActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataPekerjaanActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataPribadiActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingInformationActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingInputDataActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingOtpActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingOtpPrivyActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingPendingActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingReceiptActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingRekeningActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingPilihKantorActivity;
import id.co.bri.brimo.ui.activities.brivarevamp.FormBrivaRevampActivity;
import id.co.bri.brimo.ui.activities.brivarevamp.InquiryBrivaOpenRevampActivity;
import id.co.bri.brimo.ui.activities.brivarevamp.TambahDaftarBrivaRevampActivity;
import id.co.bri.brimo.ui.activities.bukaValas.FormBukaValasActivity;
import id.co.bri.brimo.ui.activities.bukaValas.InquiryBukaValasActivity;
import id.co.bri.brimo.ui.activities.bukaValas.KonfirmasiBukaValasActivity;
import id.co.bri.brimo.ui.activities.bukaValas.PendingBukaValasActivity;
import id.co.bri.brimo.ui.activities.bukaValas.PilihJenisTabunganValas;
import id.co.bri.brimo.ui.activities.bukaValas.PilihKantorValas;
import id.co.bri.brimo.ui.activities.bukaValas.RecieptBukaValasActivity;
import id.co.bri.brimo.ui.activities.bukarekening.InquiryProductTabunganActivity;
import id.co.bri.brimo.ui.activities.bukarekening.KonfirmasiTabunganRevActivity;
import id.co.bri.brimo.ui.activities.bukarekening.PendingTabunganActivity;
import id.co.bri.brimo.ui.activities.bukarekening.PilihanKantorGeneralActivity;
import id.co.bri.brimo.ui.activities.bukarekening.ProductBriefBukaRekeningActivity;
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity;
import id.co.bri.brimo.ui.activities.bukarekening.britamajuniorevamp.InquiryBritamaJunioRevActivity;
import id.co.bri.brimo.ui.activities.bukarekening.britamarencanarevamp.InquiryBritamaRencanaActivity;
import id.co.bri.brimo.ui.activities.bukarekening.bukavalasrevamp.FormBukaValasRevActivity;
import id.co.bri.brimo.ui.activities.bukarekening.bukavalasrevamp.InquiryBukaValasRevActivity;
import id.co.bri.brimo.ui.activities.bukarekening.bukavalasrevamp.KonfirmasiBukaValasRevActivity;
import id.co.bri.brimo.ui.activities.cardless.FormSetorTunaiActivity;
import id.co.bri.brimo.ui.activities.cardless.SetorTunaiActivity;
import id.co.bri.brimo.ui.activities.cc_sof.EstatementCcActivity;
import id.co.bri.brimo.ui.activities.cc_sof.FormAktivasiCcSofActivity;
import id.co.bri.brimo.ui.activities.cc_sof.InfoRekeningCcSofActivity;
import id.co.bri.brimo.ui.activities.cc_sof.RekeningCcSofActivity;
import id.co.bri.brimo.ui.activities.cc_sof.SyaratKetentuanCcActivity;
import id.co.bri.brimo.ui.activities.cc_sof.VerifikasiOtpBindingCcActivity;
import id.co.bri.brimo.ui.activities.ccqrismpm.SofQrisActivity;
import id.co.bri.brimo.ui.activities.dashboardInvestasi.DashboardInvestasiActivity;
import id.co.bri.brimo.ui.activities.dashboardrevamp.MenuListGeneralActivity;
import id.co.bri.brimo.ui.activities.deposito.DepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.DetailDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.InfoBukaDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.InquiryOpenDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.InquiryPenutupanDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.PendingDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.TambahDaftarDepositoActivity;
import id.co.bri.brimo.ui.activities.deposito.UbahPerpanjanganDepositoActivity;
import id.co.bri.brimo.ui.activities.depositorevamp.DashboardDepositoActivity;
import id.co.bri.brimo.ui.activities.depositorevamp.DetailDepositoRevampActivity;
import id.co.bri.brimo.ui.activities.depositorevamp.OnBoardingDepositoRevampActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.FormDompetDigitalRevamp;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.InputNomorActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.InquiryDompetDigitalRevampActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalrevamp.PilihWalletActivity;
import id.co.bri.brimo.ui.activities.donasirevamp.InquiryDonasiRevampActivity;
import id.co.bri.brimo.ui.activities.donasirevamp.ListLembagaDonasiRevampActivity;
import id.co.bri.brimo.ui.activities.dplk.DescriptionDplkProductActivity;
import id.co.bri.brimo.ui.activities.dplk.FormOpenDplkActivity;
import id.co.bri.brimo.ui.activities.dplk.InfoDplkActivity;
import id.co.bri.brimo.ui.activities.dplk.InquiryOpenDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.BrifineKombinasiRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DashboardDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DetailBrifineDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FirstTimeDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FormPilihBrifineRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.FormTopupDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.InquiryAutoPaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.InquiryDplkRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.KonfirmasiInquiryGeneralActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.OnboardingAutopaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.RiwayatSetoranBrifineDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.SettingAutoPaymentActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.SimulasiBrifineDplkActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.TambahDaftarDplkActivity;
import id.co.bri.brimo.ui.activities.emas.DashboardEmasActivity;
import id.co.bri.brimo.ui.activities.emas.DetailRekeningEmasActivity;
import id.co.bri.brimo.ui.activities.emas.EditSetoranEmasActivity;
import id.co.bri.brimo.ui.activities.emas.FormBeliEmasActivity;
import id.co.bri.brimo.ui.activities.emas.FormEditSavedEmasActivity;
import id.co.bri.brimo.ui.activities.emas.FormJualEmasActivity;
import id.co.bri.brimo.ui.activities.emas.GrafikEmasInfoActivity;
import id.co.bri.brimo.ui.activities.emas.InformasiPribadiActivity;
import id.co.bri.brimo.ui.activities.emas.InquiryBeliEmasActivity;
import id.co.bri.brimo.ui.activities.emas.InquiryCetakEmasActivity;
import id.co.bri.brimo.ui.activities.emas.InquiryRegisEmasActivity;
import id.co.bri.brimo.ui.activities.emas.OnboardEmsActivity;
import id.co.bri.brimo.ui.activities.emas.PilihKantorPegadaianActivity;
import id.co.bri.brimo.ui.activities.emas.ProductBriefEmasActivity;
import id.co.bri.brimo.ui.activities.emas.StatusAmbilFisikActivity;
import id.co.bri.brimo.ui.activities.emas.TambahDaftarEmasActivity;
import id.co.bri.brimo.ui.activities.finansialrek.InputDataFinansialActivity;
import id.co.bri.brimo.ui.activities.finansialrek.PerekamanVideoFinansialActivity;
import id.co.bri.brimo.ui.activities.finansialrek.WaitingFinansialActivity;
import id.co.bri.brimo.ui.activities.general.GeneralSNKWithPinActivity;
import id.co.bri.brimo.ui.activities.general.GeneralSNKwithChecklistActivity;
import id.co.bri.brimo.ui.activities.general.ReceiptStatusActivity;
import id.co.bri.brimo.ui.activities.halamancarirevamp.HalamanCariRevampActivity;
import id.co.bri.brimo.ui.activities.ibbiz.DataPerusahaanActivity;
import id.co.bri.brimo.ui.activities.ibbiz.IbbizActivity;
import id.co.bri.brimo.ui.activities.ibbiz.KonfirmasiIbbizActivity;
import id.co.bri.brimo.ui.activities.ibbiz.OnboardingIbbizActivity;
import id.co.bri.brimo.ui.activities.ibbiz.RekeningIbbizActivity;
import id.co.bri.brimo.ui.activities.kesehatan.KonfirmasiLayananJanjiDokterActivity;
import id.co.bri.brimo.ui.activities.launcher.BrowserIntentActivity;
import id.co.bri.brimo.ui.activities.lifestyle.DashboardLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.KonfirmasiLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.MyLifestyleTransactionActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ReceiptLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.WebviewLifestyleActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.KonfirmasiWebviewEkspedisiActivity;
import id.co.bri.brimo.ui.activities.lifestyle.ekspedisi.WebviewEkspedisiActivity;
import id.co.bri.brimo.ui.activities.lifestyle.shopping.ShoppingConfirmationActivity;
import id.co.bri.brimo.ui.activities.lifestyle.shopping.WebviewShoppingActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.CetakTokenRevampActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.FormListrikRevampActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.InquiryListrikRevampActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.TambahDaftarListrikRevampActivity;
import id.co.bri.brimo.ui.activities.lupapassword.FormLupaPasswordActivity;
import id.co.bri.brimo.ui.activities.lupapassword.KonfirmasiLupaUserPassActivity;
import id.co.bri.brimo.ui.activities.lupapassword.WaitingLupaPasswordActivity;
import id.co.bri.brimo.ui.activities.lupausername.FormLupaUsernameActivity;
import id.co.bri.brimo.ui.activities.lupausername.VerifikasiOtpEmailActivity;
import id.co.bri.brimo.ui.activities.lupausername.VerifikasiOtpNoHpActivity;
import id.co.bri.brimo.ui.activities.lupausername.WaitingLupaUsernameActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingCameraActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingCheckPointActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingConfirmPinActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataAlamatActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataKeuanganActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataPekerjaanActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingDataPribadiActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingInformationActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingInputDataActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingOtpActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingOtpPrivyActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingPendingActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingPilihKantorActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingProductTabunganActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingReceiptActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingRekeningActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingSyaratKetentuanActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingTabunganActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingUserPassActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingVerifyEmailActivity;
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingVerifyWajahActivity;
import id.co.bri.brimo.ui.activities.openaccount.InquiryGeneralOpenAccountActivity;
import id.co.bri.brimo.ui.activities.openaccount.KonfirmasiGeneralOpenAccountActivity;
import id.co.bri.brimo.ui.activities.openaccount.PendingGeneralOpenAccountActivity;
import id.co.bri.brimo.ui.activities.pajakhoreka.FormPajakHorekaActivity;
import id.co.bri.brimo.ui.activities.parking.InquiryScanParkingActivity;
import id.co.bri.brimo.ui.activities.pbb.FormPbbActivity;
import id.co.bri.brimo.ui.activities.pbb.TambahDaftarPbbActivity;
import id.co.bri.brimo.ui.activities.pendidikanrevamp.FormPendidikanRevampActivity;
import id.co.bri.brimo.ui.activities.pendidikanrevamp.TambahPendidikanRevampActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.DetailKelolaKartuNewActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.DetailLimitCardActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.InfoUbahPinAtmActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.InputNomorKtpActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.KonfirmUbahPinAtmActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.ListCardTypesActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.PengelolaanKartuNewActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.UbahPinAtmActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.VerifikasiOtpReissueActivity;
import id.co.bri.brimo.ui.activities.pengkiniandata.FormInformasiPekerjaanPengkinianActivity;
import id.co.bri.brimo.ui.activities.pengkiniandata.KonfirmasiPengkinianActivity;
import id.co.bri.brimo.ui.activities.pengkiniandata.PengkinianDataActivity;
import id.co.bri.brimo.ui.activities.portofolioksei.DashboardKseiActivity;
import id.co.bri.brimo.ui.activities.portofolioksei.SyaratRegisKseiActivity;
import id.co.bri.brimo.ui.activities.property.FormPropertyActivity;
import id.co.bri.brimo.ui.activities.property.InputBillingPropertyActivity;
import id.co.bri.brimo.ui.activities.pulsadata.FormPulsaDataRevActivity;
import id.co.bri.brimo.ui.activities.pulsadata.InquiryPulsaRevampRevActivity;
import id.co.bri.brimo.ui.activities.qr.PendingTransferQrActivity;
import id.co.bri.brimo.ui.activities.qr.crossborder.InquiryQrCrossborderActivity;
import id.co.bri.brimo.ui.activities.qr.crossborder.KonfirmasiQrCrossborderActivity;
import id.co.bri.brimo.ui.activities.qrdagang.HistoryMerchantActivity;
import id.co.bri.brimo.ui.activities.qrdagang.QrScannerActivity;
import id.co.bri.brimo.ui.activities.rdn.CekStatusRdnActivity;
import id.co.bri.brimo.ui.activities.rdn.DashboardRDNActivity;
import id.co.bri.brimo.ui.activities.rdn.DescriptionRdnProductActivity;
import id.co.bri.brimo.ui.activities.rdn.InquiryRdnRevampActivity;
import id.co.bri.brimo.ui.activities.rdn.MutasiRdnActivity;
import id.co.bri.brimo.ui.activities.rdn.Step10RdnRiskProfile;
import id.co.bri.brimo.ui.activities.rdn.Step11RdnMoreInformationActivity;
import id.co.bri.brimo.ui.activities.rdn.Step12RdnSignatureActivity;
import id.co.bri.brimo.ui.activities.rdn.Step4RdnResultKtpActivity;
import id.co.bri.brimo.ui.activities.rdn.Step7RdnSelfieResultActivity;
import id.co.bri.brimo.ui.activities.rdn.Step8FormRdnActivity;
import id.co.bri.brimo.ui.activities.rdn.TopupRdnActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptAbnormalRevampActivity;
import id.co.bri.brimo.ui.activities.receipt.ReceiptRevampActivity;
import id.co.bri.brimo.ui.activities.registrasi.nds.RegisOtpNdsActivity;
import id.co.bri.brimo.ui.activities.registrasi.nds.RegisUsernameNdsActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiBCFUActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiCameraActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiCheckPointActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiConfirmPinActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiDokumenActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiInputDataActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiMNVActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiOtpActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiOtpPrivyActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiPendingActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiUserPassActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiVerifyEmailActivity;
import id.co.bri.brimo.ui.activities.registrasirevamp.RegistrasiVerifyWajahActivity;
import id.co.bri.brimo.ui.activities.rtgs.DetailTransferRtgsActivity;
import id.co.bri.brimo.ui.activities.rtgs.InquiryTransferRtgsActivity;
import id.co.bri.brimo.ui.activities.saldodompetdigital.HubungkanDompetDigitalActivity;
import id.co.bri.brimo.ui.activities.saldodompetdigital.SmartTopUpEwalletActivity;
import id.co.bri.brimo.ui.activities.saldodompetdigital.WebViewBindingActivity;
import id.co.bri.brimo.ui.activities.sbn.BeliSBNActivity;
import id.co.bri.brimo.ui.activities.sbn.ConfirmationESBNActivity;
import id.co.bri.brimo.ui.activities.sbn.DashboardESBNActivity;
import id.co.bri.brimo.ui.activities.sbn.DetailPembelianSbnActivity;
import id.co.bri.brimo.ui.activities.sbn.InquiryMpnSbnActivity;
import id.co.bri.brimo.ui.activities.sbn.RiwayatPembelianActivity;
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.ConfirmationErlyRedeemActivity;
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.DetailSbnActivity;
import id.co.bri.brimo.ui.activities.sbn.earlyredemption.InquiryEarlyRedeemActivity;
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnDataPekerjaanActivity;
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnInquiryActivity;
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnOnboardingRegisActivity;
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnProductBriefRegisActivity;
import id.co.bri.brimo.ui.activities.sbn.regisSbn.EsbnRdnKonfirmasiActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.DashboardSbnRevampActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.DetailPortoSbnActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.FirstTimeSBNRevampActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.ListBeliSbnRevampActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.ListPortoSbnRevampActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.SbnSimulasiRevampActivity;
import id.co.bri.brimo.ui.activities.signal.FormSignalActivity;
import id.co.bri.brimo.ui.activities.simpedes.DetailImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.EditNamaImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.FormAsuransiAmkkmActivity;
import id.co.bri.brimo.ui.activities.simpedes.FormBrifineActivity;
import id.co.bri.brimo.ui.activities.simpedes.FormImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.FormPencairanImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.FormTopupImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.InfoImpianActivity;
import id.co.bri.brimo.ui.activities.simpedes.InfoSimpedesActivity;
import id.co.bri.brimo.ui.activities.simpedes.InquiryAsuransiAmkkmActivity;
import id.co.bri.brimo.ui.activities.simpedes.InquiryBrifineActivity;
import id.co.bri.brimo.ui.activities.simpedes.InquirySimpedesOpenActivity;
import id.co.bri.brimo.ui.activities.simpedes.KonfirmasiAsuransiAmkkmActivity;
import id.co.bri.brimo.ui.activities.simpedes.KonfirmasiBrifineActivity;
import id.co.bri.brimo.ui.activities.simpedes.KonfirmasiSimpedesActivity;
import id.co.bri.brimo.ui.activities.simpedes.ProductListAmkkmActivity;
import id.co.bri.brimo.ui.activities.ssc.BaseComplaintActivity;
import id.co.bri.brimo.ui.activities.ssc.ComplaintCekBrizziActivity;
import id.co.bri.brimo.ui.activities.ssc.ComplaintNonTransaksiActivity;
import id.co.bri.brimo.ui.activities.ssc.InformasiTransaksiActivity;
import id.co.bri.brimo.ui.activities.ssc.ListComplainActivity;
import id.co.bri.brimo.ui.activities.ssc.PengaduanTransaksiActivity;
import id.co.bri.brimo.ui.activities.ssc.PengaduanTrxGagalActivity;
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity;
import id.co.bri.brimo.ui.activities.tartun.FormTarikTunaiActivity;
import id.co.bri.brimo.ui.activities.tartun.ReceiptTarikActivity;
import id.co.bri.brimo.ui.activities.topuprencana.TopUpPlanActivity;
import id.co.bri.brimo.ui.activities.topuprevamp.TopUpRevampActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.EditTfInternationalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.FormTfInternasionalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.InquiryTfInternasionalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.KonfirmasiTransferInternasionalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.SaveTfInternasionalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.SavedTfInternationalActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.TransferStepDuaActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.TransferStepSatuActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.TransferStepTigaActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.DetailAftActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.FormEditSavedRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.FormTransferAliasRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.InquiryTransferRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.KonfirmasiAftActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.ListAftActivity;
import id.co.bri.brimo.ui.activities.travel.BusInquiryActivity;
import id.co.bri.brimo.ui.activities.travel.FormBusActivity;
import id.co.bri.brimo.ui.activities.travel.FormPemesananTiketActivity;
import id.co.bri.brimo.ui.activities.travel.HistoryTravelActivity;
import id.co.bri.brimo.ui.activities.travel.InquiryBusActivity;
import id.co.bri.brimo.ui.activities.travel.InquiryBusPulangActivity;
import id.co.bri.brimo.ui.activities.travel.KonfirmasiTravelAvtivity;
import id.co.bri.brimo.ui.activities.travel.KonfirmasiTravelTrainActivity;
import id.co.bri.brimo.ui.activities.travel.PickSeatActivity;
import id.co.bri.brimo.ui.activities.travel.TrainInquiryActivity;
import id.co.bri.brimo.ui.activities.travel.TravelMenuActivity;
import id.co.bri.brimo.ui.activities.travel.WebViewTravelActivity;
import id.co.bri.brimo.ui.activities.travel.WebviewKonfirmasiActivity;
import id.co.bri.brimo.ui.activities.ubahdetailrencana.ChangeDetailPlanActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.ConfirmationPageVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.DetailProductVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.DetailVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.InputLabelVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.OnBoardingVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.SelectProductVDCActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.SuccessPageVDCActivity;
import id.co.bri.brimo.ui.activities.voiceassistant.VoiceAssistantActivity;
import id.co.bri.brimo.ui.activities.voiceassistant.VoiceAssistantRevampActivity;
import id.co.bri.brimo.ui.activities.voucher.CaraRedeemVocActivity;
import id.co.bri.brimo.ui.activities.voucher.DetailVoucherActivity;
import id.co.bri.brimo.ui.activities.voucher.HistoryVoucherActivity;
import id.co.bri.brimo.ui.activities.voucher.InquiryKonfirmasiVoucherActivity;
import id.co.bri.brimo.ui.activities.voucher.VoucherActivity;
import id.co.bri.brimo.ui.activities.waiting.OtpSmsActivity;
import id.co.bri.brimo.ui.activities.waiting.WaitingActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogPromo;
import id.co.bri.brimo.ui.fragments.SavedListSearchFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatClaimDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatProsesClaimDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.MutasiOnDetailBrifineFragment;
import id.co.bri.brimo.ui.fragments.dashboardLifestyle.SubMenuLifestyleFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.PerformanceDetailDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.SimulasiDetailDplkFragment;
import id.co.bri.brimo.ui.fragments.onboarding.OnboardingKodePosBottomFragment;
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.DetailRencanaRevampFragment;
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.MutasiRencanaFragment;
import id.co.bri.brimo.ui.fragments.AnggaranFragment;
import id.co.bri.brimo.ui.fragments.BottomFragmentGantiAkun;
import id.co.bri.brimo.ui.fragments.BottomFragmentKsei;
import id.co.bri.brimo.ui.fragments.BottomFragmentLogin;
import id.co.bri.brimo.ui.fragments.BottomFragmentLoginEditFastMenu;
import id.co.bri.brimo.ui.fragments.CashbackPromoFragment;
import id.co.bri.brimo.ui.fragments.CatatanPemasukanFragment;
import id.co.bri.brimo.ui.fragments.CatatanPengeluaranFragment;
import id.co.bri.brimo.ui.fragments.CategoryVoipFragment;
import id.co.bri.brimo.ui.fragments.DetailFragment;
import id.co.bri.brimo.ui.fragments.DetailMutasiFragment;
import id.co.bri.brimo.ui.fragments.DownloadMutationFragment;
import id.co.bri.brimo.ui.fragments.FragmentBankSearch;
import id.co.bri.brimo.ui.fragments.FragmentGetPoint;
import id.co.bri.brimo.ui.fragments.FragmentGetPointAsal;
import id.co.bri.brimo.ui.fragments.FragmentGetPointTujuan;
import id.co.bri.brimo.ui.fragments.FragmentJalur;
import id.co.bri.brimo.ui.fragments.FragmentListCity;
import id.co.bri.brimo.ui.fragments.InboxFragment;
import id.co.bri.brimo.ui.fragments.KodePosBottomFragment;
import id.co.bri.brimo.ui.fragments.KodePosRevampFragment;
import id.co.bri.brimo.ui.fragments.ListRekeningCcFragment;
import id.co.bri.brimo.ui.fragments.ListRekeningFragment;
import id.co.bri.brimo.ui.fragments.PendapatanFragment;
import id.co.bri.brimo.ui.fragments.PengeluaranFragment;
import id.co.bri.brimo.ui.fragments.PickIncomeFragment;
import id.co.bri.brimo.ui.fragments.PickKategoryFragment;
import id.co.bri.brimo.ui.fragments.PickPaymentFragment;
import id.co.bri.brimo.ui.fragments.PilihanKodePosFragment;
import id.co.bri.brimo.ui.fragments.PromoAllFragment;
import id.co.bri.brimo.ui.fragments.QrMPMCodeFragment;
import id.co.bri.brimo.ui.fragments.QrMPMScanFragment;
import id.co.bri.brimo.ui.fragments.QrTransferCodeFragment;
import id.co.bri.brimo.ui.fragments.QrTransferScanFragment;
import id.co.bri.brimo.ui.fragments.RateUsBottomFragment;
import id.co.bri.brimo.ui.fragments.SetFastMenuFragment;
import id.co.bri.brimo.ui.fragments.SliderFirstFragment;
import id.co.bri.brimo.ui.fragments.SliderSeconFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragment;
import id.co.bri.brimo.ui.fragments.SumberDanaFragmentRevamp;
import id.co.bri.brimo.ui.fragments.SumberDanaValasFragment;
import id.co.bri.brimo.ui.fragments.TransaksiPemasukanFragment;
import id.co.bri.brimo.ui.fragments.TransaksiPengeluaranFragment;
import id.co.bri.brimo.ui.fragments.TransferAliasFragment;
import id.co.bri.brimo.ui.fragments.applyccrevamp.ApplyVccKodePosBottomFragment;
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.DetailRencanaRevampFragment;
import id.co.bri.brimo.ui.fragments.britamarencanarevamp.MutasiRencanaFragment;
import id.co.bri.brimo.ui.fragments.cc.DetailCcFragment;
import id.co.bri.brimo.ui.fragments.cc.MutasiCcFragment;
import id.co.bri.brimo.ui.fragments.dashboard.DashboardInvestasiFragment;
import id.co.bri.brimo.ui.fragments.dashboardLifestyle.PromoLifestyleFragment;
import id.co.bri.brimo.ui.fragments.dashboardLifestyle.RepurchaseLifestyleFragment;
import id.co.bri.brimo.ui.fragments.dashboardLifestyle.SubMenuLifestyleFragment;
import id.co.bri.brimo.ui.fragments.dashboardrevamp.DashboardRevampFragment;
import id.co.bri.brimo.ui.fragments.donasirevamp.DonasiRevampFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.DetailBrifineDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.ListDplkOptionRevampFragment;
import id.co.bri.brimo.ui.fragments.emas.*;

import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatClaimDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.HistoryRiwayatProsesClaimDplkFragment;
import id.co.bri.brimo.ui.fragments.dplkrevamp.MutasiOnDetailBrifineFragment;
import id.co.bri.brimo.ui.fragments.emas.RiwayatAmbilFisikEmasFragment;
import id.co.bri.brimo.ui.fragments.emas.RiwayatBeliEmasFragment;
import id.co.bri.brimo.ui.fragments.emas.RiwayatJualEmasFragment;
import id.co.bri.brimo.ui.fragments.esbn.HistorySbnFragment;
import id.co.bri.brimo.ui.fragments.esbn.HistorySbnFragment2;
import id.co.bri.brimo.ui.fragments.lifestyle.PendingTransactionFragment;
import id.co.bri.brimo.ui.fragments.lifestyle.PurchaseHistoryTransactionFragment;
import id.co.bri.brimo.ui.fragments.onboarding.OnboardingKodePosBottomFragment;
import id.co.bri.brimo.ui.fragments.profilerevamp.ProfileRevampFragment;
import id.co.bri.brimo.ui.fragments.qrdagang.AkunMerchantFragment;
import id.co.bri.brimo.ui.fragments.qrdagang.DashboardMerchantFragment;
import id.co.bri.brimo.ui.fragments.qrdagang.HistoryMerchantFragment;
import id.co.bri.brimo.ui.fragments.qrdagang.ReportMerchantFragment;
import id.co.bri.brimo.ui.fragments.rdn.SumberDanaRdnFragment;
import id.co.bri.brimo.ui.fragments.transferinternasional.FormTfInternasionalFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.TransferBaruKontakFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.TransferBaruNorekFragment;
import id.co.bri.brimo.ui.fragments.transferrevamp.TransferDalamRevampFragment;
import id.co.bri.brimo.ui.fragments.travel.HistoryTravelPayedFragment;
import id.co.bri.brimo.ui.fragments.travel.HistoryTravelPendingFragment;
import id.co.bri.brimo.util.singalarity.helper.IC2KeyHelper;
import io.reactivex.disposables.CompositeDisposable;

@PerActivity
@Component(dependencies = ApplicationComponent.class, modules = {ActivityModule.class})
public interface ActivityComponent {

    void inject(VoiceAssistantRevampActivity target);

    void inject(VoiceAssistantActivity target);

    void inject(DetailAkunActivity target);

    void inject(KonfirmasiTransferActivity target);

    void inject(RekeningCcSofActivity target);

    void inject(FormAktivasiCcSofActivity target);

    void inject(InfoRekeningCcSofActivity target);

    void inject(InquiryTransferActivity target);

    void inject(UbahPinActivity target);

    void inject(UbahPinValidasiActivity target);

    void inject(UbahPinBaruActivity target);

    void inject(UbahPinBaru2Activity target);

    void inject(TambahDaftarTransferActivity target);

    //// RDN ////
    void inject(DashboardRDNActivity target);

    void inject(CekStatusRdnActivity target);

    void inject(DescriptionRdnProductActivity target);

    void inject(Step4RdnResultKtpActivity target);

    void inject(Step7RdnSelfieResultActivity target);

    void inject(Step10RdnRiskProfile target);

    void inject(Step12RdnSignatureActivity target);

    void inject(Step11RdnMoreInformationActivity target);

    void inject(Step8FormRdnActivity target);

    void inject(EsbnRdnKonfirmasiActivity target);

    ////  transfer old /////

    void inject(AskActivity target);

    void inject(PilihRekeningActivity target);

    void inject(BaseActivity mainActivity);

    void inject(InquiryGeneralOpenAccountActivity target);

    void inject(PendingGeneralOpenAccountActivity target);

    void inject(KonfirmasiGeneralOpenAccountActivity target);

    void inject(PengeluaranFragment target);


    void inject(AnggaranFragment fragment);

    void inject(PendapatanFragment fragment);

    void inject(CatatanPengeluaranFragment target);

    void inject(CatatanPemasukanFragment target);

    void inject(PickPaymentFragment target);

    void inject(TransaksiPemasukanFragment target);

    void inject(PickIncomeFragment target);

    void inject(TransaksiPengeluaranFragment target);

    void inject(PickKategoryFragment target);

    void inject(SliderSeconFragment target);

    void inject(SliderFirstFragment traget);

    void inject(DetailMutasiFragment traget);

    void inject(RequestDownloadMutationActivity traget);

    void inject(DashboardIBActivity target);

    void inject(TransferAliasFragment target);

    void inject(CatatanKeuanganActivity target);

    void inject(CatatanKeuanganDetailActivity target);

    void inject(CatatanKeuanganEditActivity target);

    void inject(FastMenuActivity target);

    void inject(EditFastMenuActivity target);

    void inject(EditFastMenuRevampActivity target);

    void inject(LoginActivity target);

    void inject(UbahPasswordActivity target);

    void inject(UbahPasswordBaruActivity target);

    void inject(UbahPasswordOtpActivity target);

    void inject(WaitingActivity target);

    void inject(SumberDanaFragment target);

    void inject(ListRekeningFragment target);

    void inject(SumberDanaValasFragment target);

    void inject(RekeningActivity target);

    void inject(RekeningAkunActivity target);

    void inject(PendingTransferBankLainActivity target);

    void inject(FormEditSavedActivity target);

    void inject(EditAlliasActivity target);

    void inject(SetFastMenuFragment target);

    void inject(InboxFragment target);

    void inject(InboxFilterActivity target);

    void inject(PusatBantuanActivity target);

    void inject(SplashScreenActivity target);

    void inject(SyaratKetentuanActivity target);

    void inject(FormPulsaPaketActivity target);

    void inject(PilihAtmActivity target);

    void inject(PilihanKantorTerdekatActivity target);

    void inject(DetailFragment target);

    void inject(BlokirKartuActivity target);

    void inject(InfoRekeningActivity target);

    void inject(WaitingFinansialActivity target);

    void inject(InputDataFinansialActivity target);

    void inject(PerekamanVideoFinansialActivity target);

    void inject(ProductWebviewActivity target);

    void inject(PFMCategoryDetailActivity target);

    // QR CPM

    void inject(FormQrActivity target);

    void inject(InquiryQrActivity target);

    ////// QR Transfer //////
    void inject(QrTransferCodeTambahActivity target);

    void inject(QrTransferScanFragment target);

    void inject(QrTransferCodeFragment target);

    ////// QR MPM //////////
    void inject(QrMPMActivity target);

    void inject(QrMPMScanFragment target);

    void inject(QrMPMCodeTambahActivity target);

    void inject(QrMPMCodeFragment target);

    void inject(PendingTransferQrActivity target);

    void inject(InquiryQrMPMActivity target);

    ////// NFC QR TAP ////////

    void inject(NfcPaymentActivity target);

    ////// TRANSFER ////////

    void inject(TambahTransferAliasActivity target);

    ////// BRIVA ///// BRIVA ////////

    void inject(FormBrivaActivity target);

    void inject(TambahDaftarBrivaActivity target);

    /////// TELKOM ////////
    void inject(FormTelkomActivity target);

    void inject(TambahDaftarTelkomActivity target);

    /////// KAI ////////
    void inject(FormKaiActivity target);

    /////// WALLET ////////

    void inject(FormWalletActivity target);

    void inject(TambahDaftarWalletActivity target);

    /////// KREDIT ////////

    void inject(FormKreditActivity target);

    void inject(TambahDaftarKreditActivity target);

    /////// BRIZZI ////////

    void inject(FormBrizziActivity target);

    void inject(FormBrizziManualActivity target);

    void inject(CekBrizziSatuActivity target);

    void inject(CekBrizziDuaActivity target);

    void inject(CekBrizziDariLuarActivity target);

    void inject(InfoSaldoBrizzi target);

    void inject(InquiryBrizziActivity target);

    void inject(KonfirmasiBrizziOnlineActivity target);

    void inject(TapBrizziAktivasiActivity target);

    void inject(TapBrizziAktivasiNonUserActivity target);

    /////// PLN ////////

    void inject(FormPlnTokenActivity target);

    void inject(TambahDaftarPlnActivity target);

    void inject(InquiryPlnTokenActivity target);

    void inject(CetakTokenActivity target);

    /////// MUTASI ////////

    void inject(MutasiActivity target);

    ////// QR Merchant //////

    void inject(DashboardMerchantFragment target);

    void inject(AkunMerchantFragment target);

    void inject(QrScannerActivity target);

    void inject(HistoryMerchantFragment target);

    void inject(ReportMerchantFragment target);

    void inject(HistoryMerchantActivity target);


    /////// Pasca Bayar //////////

    void inject(FormPascaBayarActivity target);

    void inject(TambahDaftarPascaActivity target);

    /////// Asuransi //////////

    void inject(FormAsuransiActivity target);

    void inject(ListProdukAsuransiActivity target);

    void inject(JenisTransaksiAsuransiActivity target);

    void inject(InquiryMicrositeBrivaActivity target);

    void inject(MicrositeActivity target);

    void inject(MicrositeLamaActivity target);

    void inject(TambahDaftarAsuransiActivity target);

    //// TArik Tunai ////
    void inject(FormTarikTunaiActivity target);

    void inject(KonfirmasiTarikActivity target);

    void inject(ReceiptTarikActivity target);

    //// Cicilan Finance /////
    void inject(FormCicilanActivity target);

    void inject(TambahDaftarCicilanActivity target);

    // DPLK
    void inject(InfoDplkActivity target);

    void inject(FormDplkActivity target);

    void inject(TambahDplkActivity target);

    void inject(DescriptionDplkProductActivity target);

    void inject(FormOpenDplkActivity target);

    void inject(InquiryOpenDplkActivity target);

    // DPLK REVAMP
    void inject(InquiryAutoPaymentActivity target);

    void inject(SettingAutoPaymentActivity target);

    void inject(OnboardingAutopaymentActivity target);

    void inject(FormPilihBrifineRevampActivity target);

    void inject(ListDplkOptionRevampActivity target);

    void inject(DashboardDplkRevampActivity target);

    void inject(DetailBrifineDplkRevampActivity target);

    void inject(DetailBrifineDplkFragment target);

    void inject(FirstTimeDplkActivity target);

    void inject(KonfirmasiInquiryGeneralActivity target);

    void inject(ListDplkOptionRevampFragment target);

    void inject(HistoryRiwayatClaimDplkFragment target);

    void inject(HistoryRiwayatProsesClaimDplkFragment target);

    void inject(SimulasiBrifineDplkActivity target);

    void inject(BrifineKombinasiRevampActivity target);

    void inject(FormTopupDplkActivity target);

    void inject(TambahDaftarDplkActivity target);

    void inject(ReceiptStatusActivity target);


    void inject(RiwayatSetoranBrifineDplkActivity target);

    void inject(MutasiOnDetailBrifineFragment target);

    void inject(InquiryDplkRevampActivity target);

    //// Televisi ////
    void inject(FormTelevisiActivity target);

    void inject(TambahDaftarTelevisiActivity target);

    //Deposito ///
    void inject(DepositoActivity target);

    void inject(InfoBukaDepositoActivity target);

    void inject(TambahDaftarDepositoActivity target);

    void inject(InquiryOpenDepositoActivity target);

    void inject(InquiryPenutupanDepositoActivity target);

    void inject(PendingDepositoActivity target);

    void inject(DetailDepositoActivity target);

    void inject(UbahPerpanjanganDepositoActivity target);

    //DepositoRevamp
    void inject(DetailDepositoRevampActivity target);

    void inject(OnBoardingDepositoRevampActivity target);

    void inject(DashboardDepositoActivity target);

    //Donasi
    void inject(FormDonasiActivity target);

    //Donasi Revamp
    void inject(DonasiRevampFragment target);

    void inject(ListLembagaDonasiRevampActivity target);

    void inject(InquiryDonasiRevampActivity target);

    //Bpjs
    void inject(FormBpjsActivity target);

    void inject(TambahDaftarBpjsActivity target);

    //Pinjaman BRI
    void inject(InfoPinjamanActivity target);

    void inject(DetailPinjamanActivity target);

    void inject(FormBayarPinjamanActivity target);

    //Ceria

    void inject(CeriaActivity target);

    //Apply CC

    void inject(DaftarCcActivity target);

    //OneShild
    void inject(OneShildActivity target);

    //Promo
    void inject(LihatLebihPromoActivity target);

    void inject(AllPromoActivity target);

    void inject(DetailPromoActivity target);

    //MPN
    void inject(FormMpnActivity target);

    /////// GENERAL ////////// GENERAL //////////

    void inject(GeneralOtpActivity target);

    void inject(NotificationRouterActivity target);

    void inject(ReceiptActivity target);

    void inject(ReceiptInboxActivity target);

    void inject(KonfirmasiGeneralActivity target);

    void inject(BaseInquiryActivity target);

    void inject(BaseFormActivity target);

    void inject(BaseFormRevampActivity target);

    void inject(BaseFormNosavedActivity target);

    void inject(InquiryGeneralCloseActivity target);

    void inject(InquiryGeneralOpenActivity target);

    void inject(PilihKantorGeneralActivity target);

    void inject(GeneralPasswordActivity target);

    void inject(GeneralOtpDigitHpActivity target);

    void inject(GeneralWebviewActivity target);

    // regis nds
    void inject(RegisUsernameNdsActivity target);

    void inject(RegisOtpNdsActivity target);

    //Info - info
    void inject(InfoKursActivity target);

    void inject(InfoSahamActivity target);

    //Lupa Pin
    void inject(LupaPinFastActivity target);

    void inject(LupaPinActivity target);

    void inject(LupaPinOtpActivity target);

    void inject(LupaPinInputDataActivity target);

    void inject(LupaPinVerifEmailActivity target);

    //PDAM
    void inject(FormPdamActivity target);

    void inject(TambahDaftarPdamActivity target);

    // BRIGUNA DIGITAL
    void inject(BrigunaDigitalActivity target);

    // Pendidikan revamp
    void inject(FormPendidikanRevampActivity target);

    void inject(TambahPendidikanRevampActivity target);

    void inject(PilihanKodePosFragment target);

    void inject(DialogPromo target);

    void inject(NotificationListActivity target);

    //Simpedes
    void inject(InfoSimpedesActivity target);

    void inject(InfoImpianActivity target);

    void inject(FormImpianActivity target);

    void inject(DetailImpianActivity target);

    void inject(FormPencairanImpianActivity target);

    void inject(FormTopupImpianActivity target);

    void inject(EditNamaImpianActivity target);

    void inject(EditUsernameActivity target);

    void inject(KonfirmasiSimpedesActivity target);

    // Konversi Vallas

    void inject(FormKonversiVallasActivity target);

    void inject(InquiryKonversiVallasActivity target);

    void inject(KonfirmasiKonversiValasActivity target);

    void inject(InquirySimpedesOpenActivity target);

    void inject(FormBrifineActivity target);

    void inject(FormAsuransiAmkkmActivity target);

    void inject(InquiryAsuransiAmkkmActivity target);

    void inject(ProductListAmkkmActivity target);

    void inject(InquiryBrifineActivity target);

    void inject(KonfirmasiBrifineActivity target);

    void inject(KonfirmasiAsuransiAmkkmActivity target);

    //Browser
    void inject(BrowserIntentActivity target);

    //Britama Rencana

    void inject(MutasiRencanaFragment target);

    void inject(DetailRencanaRevampActivity target);

    void inject(PilihJenisTabunganActivity target);

    void inject(DetailRencanaActivity target);

    void inject(HitungRencanaActivity target);

    void inject(KonfirmasiRencanaActivity target);

    void inject(KonfirmasiDataRencanaActivity target);

    void inject(PencairanRencanaActivity target);

    void inject(InquiryPencairanRencanaActivity target);

    //Saldo dompet digital
    void inject(HubungkanDompetDigitalActivity target);

    void inject(SmartTopUpEwalletActivity target);

    void inject(WebViewBindingActivity target);

    // Rate us
    void inject(RateUsBottomFragment target);

    //LTMPT
    void inject(FormLTMPTActivity target);

    //BiFast
    void inject(AliasBiFastActivity target);

    //Remittence
    void inject(FormTfInternasionalFragment target);

    void inject(FormTfInternasionalActivity target);

    void inject(FragmentJalur target);

    void inject(TransferStepSatuActivity target);

    void inject(TransferStepDuaActivity target);

    void inject(TransferStepTigaActivity target);

    void inject(SaveTfInternasionalActivity target);

    void inject(InquiryTfInternasionalActivity target);

    void inject(FragmentBankSearch target);

    void inject(KonfirmasiTransferInternasionalActivity target);

    void inject(EditTfInternationalActivity target);

    void inject(SavedTfInternationalActivity target);


    // Tartun NDS
    void inject(KonfirmasiTartunNdsActivity target);

    // SSC
    void inject(SelfServiceActivity target);

    void inject(PengaduanTrxGagalActivity target);

    void inject(PengaduanTransaksiActivity target);

    void inject(InformasiTransaksiActivity target);

    void inject(ListComplainActivity target);

    void inject(ComplaintCekBrizziActivity target);

    void inject(ComplaintNonTransaksiActivity target);

    void inject(BaseComplaintActivity target);

    // Lupa Password
    void inject(FormLupaPasswordActivity target);

    void inject(KonfirmasiLupaUserPassActivity target);

    void inject(WaitingLupaPasswordActivity target);

    // Lupa Username
    void inject(FormLupaUsernameActivity target);

    void inject(WaitingLupaUsernameActivity target);

    //Travel
    void inject(TravelMenuActivity target);

    void inject(FragmentListCity target);

    void inject(FormBusActivity target);

    void inject(InquiryBusActivity target);

    void inject(FormPemesananTiketActivity target);

    void inject(PickSeatActivity target);

    void inject(BusInquiryActivity target);

    void inject(InquiryBusPulangActivity target);

    void inject(KonfirmasiTravelAvtivity target);

    void inject(HistoryTravelActivity target);

    void inject(FragmentGetPoint target);

    void inject(FragmentGetPointAsal target);

    void inject(FragmentGetPointTujuan target);

    void inject(WebviewKonfirmasiActivity target);

    void inject(WebViewTravelActivity target);

    // iBBIZ
    void inject(OnboardingIbbizActivity target);

    void inject(RekeningIbbizActivity target);

    void inject(DataPerusahaanActivity target);

    void inject(KonfirmasiIbbizActivity target);

    // Buka Valas
    void inject(FormBukaValasActivity target);

    void inject(InquiryBukaValasActivity target);

    void inject(KonfirmasiBukaValasActivity target);

    void inject(PendingBukaValasActivity target);

    void inject(PilihJenisTabunganValas target);

    void inject(PilihKantorValas target);

    void inject(RecieptBukaValasActivity target);

    //ESBN
    void inject(DashboardESBNActivity target);

    void inject(BeliSBNActivity target);

    void inject(HistorySbnFragment target);

    void inject(HistorySbnFragment2 target);

    void inject(RiwayatPembelianActivity target);

    void inject(ConfirmationESBNActivity target);

    void inject(InquiryMpnSbnActivity target);

    void inject(DetailPembelianSbnActivity target);

    void inject(HistoryTravelPayedFragment target);

    void inject(HistoryTravelPendingFragment target);

    void inject(TrainInquiryActivity target);

    void inject(KonfirmasiTravelTrainActivity target);

    void inject(FormPbbActivity target);

    void inject(TambahDaftarPbbActivity target);

    //Esbn Regis
    void inject(EsbnOnboardingRegisActivity target);

    void inject(EsbnProductBriefRegisActivity target);

    void inject(EsbnDataPekerjaanActivity target);

    void inject(EsbnInquiryActivity target);

    //Cc SOF
    void inject(ListRekeningCcFragment target);

    //bripoin
    void inject(BripoinActivity target);

    void inject(FormSetorTunaiActivity target);

    void inject(SetorTunaiActivity target);

    // Edit Email
    void inject(EditEmailActivity target);

    void inject(Otp4DigitDefaultActivity target);

    // chat banking
    void inject(FormChatBankingActivity target);

    void inject(DetailTransferRtgsActivity target);

    void inject(InquiryTransferRtgsActivity target);

    void inject(InputNomorKtpActivity target);

    void inject(VerifikasiOtpReissueActivity target);


    // Transfer Revamp
    void inject(FormTransferAliasRevampActivity target);

    void inject(TransferDalamRevampFragment target);

    void inject(TransferBaruNorekFragment target);

    void inject(TransferBaruKontakFragment target);


    void inject(SavedListSearchFragment target);

    void inject(InquiryTransferRevampActivity target);

    void inject(FormEditSavedRevampActivity target);

    // Pulsa Data Revamp
    void inject(FormPulsaDataRevActivity target);

    void inject(InquiryPulsaRevampRevActivity target);


    //Buka Rekening Revamp
    void inject(TabunganActivity target);

    void inject(ProductBriefBukaRekeningActivity target);

    void inject(InquiryProductTabunganActivity target);

    void inject(PilihanKantorGeneralActivity target);

    void inject(KonfirmasiTabunganRevActivity target);

    void inject(PendingTabunganActivity target);

    //JUNIO

    void inject(PilihKantorJunioActivity target);

    void inject(FormBritamaJunioActivity target);

    void inject(InquiryOpenJunioActivity target);

    void inject(KonfirmasiOpenJunioActivity target);

    // Detail Junio
    void inject(DetailRekeningJunioActivity target);

    // Pengkinian Data
    void inject(PengkinianDataActivity target);

    void inject(KodePosBottomFragment target);

    void inject(FormInformasiPekerjaanPengkinianActivity target);

    void inject(KonfirmasiPengkinianActivity target);

    void inject(ListUpdateRekeningActivity target);

    void inject(ListRekeningCategoryActivity target);

    //Cashback
    void inject(PromoAllFragment target);

    void inject(CashbackAllActivity target);

    void inject(CashbackPromoFragment target);

    void inject(DownloadMutationFragment traget);

    void inject(ListCardTypesActivity target);

    // REVAMP Registrasi Brimo
    void inject(RegistrasiDokumenActivity target);

    void inject(RegistrasiCameraActivity target);

    void inject(RegistrasiInputDataActivity target);

    void inject(RegistrasiBCFUActivity target);

    void inject(RegistrasiCheckPointActivity target);

    void inject(RegistrasiOtpActivity target);

    void inject(RegistrasiVerifyEmailActivity target);

    void inject(RegistrasiVerifyWajahActivity target);

    void inject(RegistrasiUserPassActivity target);

    void inject(RegistrasiConfirmPinActivity target);

    void inject(RegistrasiOtpPrivyActivity target);

    void inject(RegistrasiPendingActivity target);

    void inject(RegistrasiMNVActivity target);

    //Briva Revamp
    void inject(FormBrivaRevampActivity target);

    void inject(TambahDaftarBrivaRevampActivity target);

    // Dompet Digital Revamp
    void inject(FormDompetDigitalRevamp target);

    void inject(PilihWalletActivity target);

    void inject(InputNomorActivity target);

    void inject(InquiryDompetDigitalRevampActivity target);

    void inject(DetailLimitCardActivity target);

    // Revamp listrik
    void inject(FormListrikRevampActivity target);

    void inject(CetakTokenRevampActivity target);

    void inject(TambahDaftarListrikRevampActivity target);

    void inject(InquiryListrikRevampActivity target);

    //Cairkan SBN

    void inject(DetailSbnActivity target);

    void inject(InquiryEarlyRedeemActivity target);

    void inject(ConfirmationErlyRedeemActivity target);

    //kontak kami

    void inject(MicrositeCallbackActivity target);

    void inject(KontakKamiActivity target);

    //Top up Rdn
    void inject(InquiryRdnRevampActivity target);

    void inject(TopupRdnActivity target);

    void inject(MutasiRdnActivity target);

    void inject(SumberDanaRdnFragment target);

    void inject(DetailPromoCashbackActivity target);

    // VOUCHER
    void inject(VoucherActivity target);

    void inject(DetailVoucherActivity target);

    void inject(InquiryKonfirmasiVoucherActivity target);

    void inject(CaraRedeemVocActivity target);

    void inject(HistoryVoucherActivity target);

    void inject(ReceiptRevampActivity target);

    void inject(SyaratKetentuanCcActivity target);

    void inject(VerifikasiOtpBindingCcActivity target);


    // Pengelolaan Kartu
    void inject(PengelolaanKartuNewActivity target);

    void inject(DetailKelolaKartuNewActivity target);

    void inject(InfoUbahPinAtmActivity target);

    void inject(UbahPinAtmActivity target);

    void inject(KonfirmUbahPinAtmActivity target);

    void inject(InquiryQrCrossborderActivity traget);

    void inject(KonfirmasiQrCrossborderActivity traget);

    // Revamp Asuransi
    void inject(DashboardAsuransiActivity target);

    //emas
    void inject(DashboardEmasActivity target);

    void inject(OnboardEmsActivity target);

    void inject(ProductBriefEmasActivity target);

    void inject(InformasiPribadiActivity target);

    void inject(InquiryRegisEmasActivity target);

    void inject(PilihKantorPegadaianActivity target);

    void inject(DetailRekeningEmasActivity target);

    void inject(EditSetoranEmasActivity target);

    void inject(InquiryBeliEmasActivity target);

    void inject(FormJualEmasActivity target);

    void inject(RiwayatBeliEmasFragment target);

    void inject(RiwayatJualEmasFragment target);

    void inject(GrafikEmasInfoActivity target);

    void inject(RiwayatAmbilFisikEmasFragment target);

    void inject(StatusAmbilFisikActivity target);

    void inject(InquiryCetakEmasActivity target);

    void inject(KonfirmasiGeneralInvestasiActivity target);

    void inject(GeneralSNKwithChecklistActivity target);

    void inject(FormBeliEmasActivity target);

    void inject(TambahDaftarEmasActivity target);

    void inject(FormEditSavedEmasActivity target);

    // Dashboard Investasi
    void inject(DashboardInvestasiActivity target);

    void inject(DashboardInvestasiFragment target);

    //PortofolioKsei
    void inject(DashboardKseiActivity target);

    void inject(SyaratRegisKseiActivity target);

    void inject(BottomFragmentKsei target);

    void inject(ListAsuransiActivity target);

    void inject(DetailAsuransiRevampActivity target);

    void inject(FormAsuransiRevampActivity target);

    void inject(DetailBeliAsuransiActivity target);

    void inject(KodePosRevampFragment target);

    void inject(InquiryBritamaJunioRevActivity target);

    // Buka Valas
    void inject(FormBukaValasRevActivity target);

    void inject(InquiryBukaValasRevActivity target);

    void inject(KonfirmasiBukaValasRevActivity target);

    void inject(InquiryBritamaRencanaActivity target);

    // scan parking
    void inject(InquiryScanParkingActivity target);

    // Top Up Rencana
    void inject(TopUpPlanActivity target);

    //Biometric
    void inject(KeamananActivity target);


    // Lifestyle
    void inject(DashboardLifestyleActivity target);

    void inject(MyLifestyleTransactionActivity target);

    void inject(PurchaseHistoryTransactionFragment target);

    void inject(PendingTransactionFragment target);

    void inject(PromoLifestyleFragment target);

    void inject(WebviewLifestyleActivity target);

    void inject(RepurchaseLifestyleFragment target);

    void inject(KonfirmasiLifestyleActivity target);

    void inject(ReceiptLifestyleActivity target);

    void inject(WebviewShoppingActivity target);

    void inject(ShoppingConfirmationActivity target);

    void inject(WebviewEkspedisiActivity target);

    void inject(KonfirmasiWebviewEkspedisiActivity target);

    void inject(SubMenuLifestyleFragment target);

    /////// GENERAL ////////// GENERAL //////////

    void inject(ReceiptAbnormalRevampActivity target);

    void inject(SumberDanaFragmentRevamp target);

    void inject(KonfirmasiGeneralRevampActivity target);

    void inject(BottomFragmentLogin target);

    void inject(BottomFragmentGantiAkun target);

    void inject(BottomFragmentLoginEditFastMenu target);

    void inject(DashboardRevampFragment target);

    void inject(MenuListGeneralActivity target);

    void inject(TopUpRevampActivity target);

    void inject(HalamanCariRevampActivity target);

    void inject(InquiryBrivaOpenRevampActivity target);

    void inject(InquiryKonfirmasiBrivaRevampCloseActivity target);

    void inject(KonfirmasiGeneralOpenRevampActivity target);

    void inject(BaseInquiryRevampActivity target);

    void inject(ChangeDetailPlanActivity target);

    void inject(GeneralSNKWithPinActivity target);

    ///// Buka Rekening Non CIF
    void inject(OnboardingRekeningActivity target);

    void inject(OnboardingCheckPointActivity target);

    void inject(OnboardingPilihKantorActivity target);

    void inject(OnboardingCameraActivity target);

    void inject(OnboardingInputDataActivity target);

    void inject(OnboardingOtpActivity target);

    void inject(OnboardingVerifyEmailActivity target);

    void inject(OnboardingVerifyWajahActivity target);

    void inject(OnboardingPendingActivity target);

    void inject(OnboardingInformationActivity target);

    void inject(OnboardingDataPribadiActivity target);

    void inject(OnboardingDataAlamatActivity target);

    void inject(OnboardingKodePosBottomFragment target);

    void inject(OnboardingDataPekerjaanActivity target);

    void inject(OnboardingDataKeuanganActivity target);

    void inject(OnboardingSyaratKetentuanActivity target);

    void inject(OnboardingUserPassActivity target);

    void inject(OnboardingConfirmPinActivity target);

    void inject(OnboardingOtpPrivyActivity target);

    void inject(OnboardingReceiptActivity target);

    void inject(DashboardRencanaActivity target);

    void inject(FirstTimeRencanaActivity target);

    void inject(OnboardingTabunganActivity target);

    void inject(OnboardingProductTabunganActivity target);

    void inject(LoadingMNVActivity target);

    void inject(OnBoardingVDCActivity target);

    void inject(SelectProductVDCActivity target);

    void inject(InputLabelVDCActivity target);

    void inject(ConfirmationPageVDCActivity target);

    void inject(DetailProductVDCActivity target);

    void inject(DetailVDCActivity target);

    void inject(SuccessPageVDCActivity target);

    void inject(FormSignalActivity target);

    void inject(VerifikasiOtpNoHpActivity target);

    void inject(IbbizActivity target);

    void inject(VerifikasiOtpEmailActivity target);

    void inject(DetailCcFragment target);

    void inject(MutasiCcFragment target);

    void inject(EstatementCcActivity target);

    void inject(FormPajakHorekaActivity target);

    void inject(TransactionLimitInformationActivity target);

    void inject(EditTargetMoActivity target);

    void inject(DetailRencanaRevampFragment target);

    void inject(KonfirmasiRencanaRevampActivity target);

    void inject(SofQrisActivity target);

    void inject(OtpSmsActivity target);

    void inject(ListMiniAppActivity target);

    void inject(ListAccountPalmActivity target);

    // Lifestyle
    void inject(KonfirmasiLayananJanjiDokterActivity target);

    // Sbn Revamp

    void inject(FirstTimeSBNRevampActivity target);

    void inject(DashboardSbnRevampActivity target);

    void inject(ListBeliSbnRevampActivity target);

    void inject(ListPortoSbnRevampActivity target);

    void inject(DetailPortoSbnActivity target);

    void inject(SbnSimulasiRevampActivity target);

    void inject(ProfileRevampFragment target);

    // Aft (transfer terjadwal)
    void inject(KonfirmasiAftActivity target);

    void inject(ListAftActivity target);

    void inject(DetailAftActivity target);

    // Auto grab fund
    void inject(DashboardAutoGrabFundActivity target);

    // Notification Setting
    void inject(NotificationSettingActivity target);

    void inject(NotificationSettingDetailActivity target);

    // CC
    void inject(ApplyVccProductListActivity target);

    void inject(ApplyVccProductDetailActivity target);

    void inject(ApplyVccInformationPersonalActivity target);

    void inject(ApplyVccInformationAddressActivity target);

    void inject(ApplyVccInformationJobActivity target);

    void inject(ApplyVccConfirmActivity target);

    void inject(ApplyVccPhotoKtpActivity target);

    void inject(ApplyVccPhotoSelfieActivity target);

    void inject(ApplyVccInfoActivity target);

    void inject(ApplyVccSofListActivity target);

    void inject(ApplyVccOtpActivity target);

    void inject(ApplyVccKodePosBottomFragment target);

    void inject(FormPropertyActivity target);

    void inject(InputBillingPropertyActivity target);

    void inject(StepPersonalDataDplkActivity target);

    void inject(StepPickProfileRiskDplkActivity target);

    void inject(PerformanceDetailDplkFragment target);

    void inject(FormDataIuranDplkActivity target);

    void inject(DetailProductFtuDplkActivity target);

    void inject(TermsAndConditionFtuDplkActivity target);

    void inject(SimulasiDetailDplkFragment target);

    void inject(ReceiptPendingRegistDplkActivity target);


    void inject(CategoryVoipFragment target);

    void inject(BirthdayOnboardingActivity target);

    //// Sub Component
    PinAllComponent plusPinComponent(PinAllModule allModule);


    //////////  APP MODULE  //////////

    BRImoPrefSource brimoPrefSource();

    CompositeDisposable compositeDisposable();

    ApiSource apiSource();

    SchedulerProvider schedulerProvider();

    CategoryPfmSource categoryPfmSource();

    TransaksiPfmSource transaksiPfmSource();

    AnggaranPfmSource anggaranPfmSource();

    FastMenuSource fastMenuSource();

    RateSource rateSource();

    MenuDashFavSource menuTitleDashSource();

    MenuDashAllSource menuSubTitleDashSource();

    Context provideContext();

    FileParameterSource fileParameterSource();

    MenuLifestyleSource menuLifestyleSource();

    IC2KeyHelper ic2KeyHelper();

    IChangeDebitPINPresenter<IChangeDebitPINView> getChangeDebitPinPresenter();

}