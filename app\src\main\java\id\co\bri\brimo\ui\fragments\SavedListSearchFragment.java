package id.co.bri.brimo.ui.fragments;

import static id.co.bri.brimo.domain.config.Constant.JourneyType.JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST;

import android.annotation.SuppressLint;
import android.app.SearchManager;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SearchView;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.ethanhua.skeleton.Skeleton;
import com.ethanhua.skeleton.SkeletonScreen;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.HistoryAdapterRevamp;
import id.co.bri.brimo.adapters.SavedAdapterRevamp;
import id.co.bri.brimo.contract.IPresenter.ISavedListSearchPresenter;
import id.co.bri.brimo.contract.IPresenter.transferrevamp.ITransferDalamRevampPresenter;
import id.co.bri.brimo.contract.IView.ISavedListSearchView;
import id.co.bri.brimo.contract.IView.transferrevamp.ITransferDalamRevampView;
import id.co.bri.brimo.databinding.FragmentSavedListSearchBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase;
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder;
import id.co.bri.brimo.models.BankModel;
import id.co.bri.brimo.models.ParameterModel;
import id.co.bri.brimo.models.apimodel.request.InquiryTransferAliasRevampRequest;
import id.co.bri.brimo.models.apimodel.response.DataListBankResponse;
import id.co.bri.brimo.models.apimodel.response.ExtrasResponse;
import id.co.bri.brimo.models.apimodel.response.HistoryResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryTransferRevampResponse;
import id.co.bri.brimo.models.apimodel.response.RestResponse;
import id.co.bri.brimo.models.apimodel.response.SavedResponse;
import id.co.bri.brimo.models.optionmodel.OptionSearchRevampModel;
import id.co.bri.brimo.ui.activities.transferrevamp.FormEditSavedRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.FormTransferAliasRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.InquiryTransferRevampActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.ListAftActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.TambahTransferAliasRevampActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp;
import id.co.bri.brimo.ui.fragments.BaseFragment;
import id.co.bri.brimo.ui.fragments.SearchRevampFragment;

public class SavedListSearchFragment extends BaseFragment implements
        ITransferDalamRevampView,
        HistoryAdapterRevamp.ClickItem, SavedAdapterRevamp.ClickItem {

    protected static final String TAG_RESPONSE = "response";

    BubbleShowCaseBuilder showBubbleFirstTimeVisit;

    private FragmentSavedListSearchBinding binding;
    protected HistoryAdapterRevamp historyAdapter;
    protected SavedAdapterRevamp savedAdapter;
    protected ArrayList<HistoryResponse> historyResponses = new ArrayList<>();
    protected ArrayList<SavedResponse> savedResponses = new ArrayList<>();
    protected List<BankModel> bankLists;
    protected static boolean isFromFastMenu = false;
    protected static boolean mIsCreateFromBanner = false;
    protected SkeletonScreen skeletonScreenHistory;
    protected SkeletonScreen skeletonScreenSaved;
    protected ExtrasResponse mAftBanner = null;
    protected String mJourneyType = "";
    protected static final String TAG_JOURNEY_TYPE = "TAG_JOURNEY_TYPE_FORM";

    @Inject
    ISavedListSearchPresenter<ISavedListSearchView> presenter;

    @SuppressLint("ValidFragment")
    public SavedListSearchFragment() {

    }

    public static SavedListSearchFragment newInstance(boolean isFromFast, String journeyType, Boolean isCreateFromBanner) {
        SavedListSearchFragment fragment = new SavedListSearchFragment();
        Bundle args = new Bundle();
        args.putString(TAG_JOURNEY_TYPE, journeyType);
        isFromFastMenu = isFromFast;
        mIsCreateFromBanner = isCreateFromBanner;
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentSavedListSearchBinding.inflate(getLayoutInflater());
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            parseDataIntent(bundle);
        }
        injectDependency();
        disableButtonSubmit(true);
        searchNamaBank();
        initiateHistoryAdapter();
        initiateSavedAdapter();
        setupBanner();
        setupCustomView();
    }

    private void parseDataIntent(Bundle extras){
        if (extras != null) {
            mJourneyType = extras.getString(TAG_JOURNEY_TYPE);
        }
    }

    @Override
    public void onDestroyView() {
        binding = null;
        super.onDestroyView();
    }

    private void setupBanner(){
        if (mJourneyType.equals(FormTransferAliasRevampActivity.mJourneyTypeList.ListAft)) return;
        if (mAftBanner != null){
//            binding.llBannerAft.setVisibility(View.VISIBLE);
            if (isFromFastMenu) {
                binding.llAddSavedList.setVisibility(View.GONE);
            } else {
                binding.llAddSavedList.setVisibility(View.VISIBLE);
            }
//            binding.tvBanner.setText(mAftBanner.getTitle());
//            binding.tvBannerDesc.setText(mAftBanner.getDesc());
//            binding.llBannerAft.setOnClickListener(v -> launchBanner());
        }
    }

    private void launchBanner(){
        if (getActivity() != null){
            ListAftActivity.launchIntent(getActivity(), true);
        }
    }

    private void setupCustomView(){
        if (mJourneyType.equals(FormTransferAliasRevampActivity.mJourneyTypeList.ListAft)) {
//            binding.llBannerAft.setVisibility(View.GONE);
            binding.llAddSavedList.setVisibility(View.GONE);
        }
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            initiateUrlPresenter();

            if (isFromFastMenu) {
                presenter.checkAccessTokenC2();
            } else {
                presenter.getDataForm();
            }
        }
    }


    /**
     * method ini digunakan untuk inisiasi URL berdasarkan flag FastMenu
     */
    private void initiateUrlPresenter() {
        presenter.setView((ISavedListSearchView) this);

        //setting URL for presenter
        if (isFromFastMenu) {
            presenter.setUrlForm(GeneralHelper.getString(R.string.url_form_transfer_alias_fm_v3));
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_transfer_inquiry_fm_v3));
        } else {
            presenter.setUrlForm(GeneralHelper.getString(R.string.url_form_transfer_alias_v3));
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_transfer_inquiry_v3));
        }

        presenter.start();
    }

    private void searchNamaBank() {
        if (getActivity() != null) {
            SearchManager searchManager = (SearchManager) getActivity().getSystemService(Context.SEARCH_SERVICE);
            binding.searchviewBriva.setSearchableInfo(searchManager.getSearchableInfo(getActivity().getComponentName()));
        }
        EditText editText = binding.searchviewBriva.findViewById(R.id.search_src_text);
        if (getActivity() != null) {
            editText.setHintTextColor(ContextCompat.getColor(getActivity(), R.color.neutral_light60));
            editText.setTypeface(ResourcesCompat.getFont(getActivity(), R.font.bri_digital_text_medium));
        }
        editText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);

        binding.searchviewBriva.setMaxWidth(Integer.MAX_VALUE);
        binding.searchviewBriva.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                Objects.requireNonNull(savedAdapter.getFilter()).filter(query);
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                Objects.requireNonNull(savedAdapter.getFilter()).filter(newText);
                return false;
            }
        });
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onSuccessGetHistoryForm(List<HistoryResponse> historyResponse) {
        historyResponses.clear();
        if (!mJourneyType.equals(FormTransferAliasRevampActivity.mJourneyTypeList.ListAft)) historyResponses.addAll(historyResponse);
        else {
            ArrayList<HistoryResponse> historyResponseArrayList = new ArrayList<>();
            for (HistoryResponse response : historyResponse) {
                if (response.getSubtitle().equals("BANK BRI")) {
                    historyResponseArrayList.add(response);
                }
            }
            historyResponses.addAll(historyResponseArrayList);
        }

        historyAdapter.setHistoryResponses(historyResponses);
        historyAdapter.notifyDataSetChanged();

//        if (historyResponses.size() > 0) {
//            binding.llNoDataHistory.setVisibility(View.GONE);
//            binding.rvBrivaTerakhir.setVisibility(View.VISIBLE);
//        } else {
//            binding.llNoDataHistory.setVisibility(View.VISIBLE);
//            binding.rvBrivaTerakhir.setVisibility(View.GONE);
//            binding.tvNoHistory.setText(String.format(GeneralHelper.getString(R.string.no_history)));
//        }
    }

    @Override
    public void isHideSkeleton(boolean hide) {
        if (hide) {
            disableButtonSubmit(false);
            cekFromFastMenu();
            skeletonScreenHistory.hide();
            skeletonScreenSaved.hide();
        } else {
            disableButtonSubmit(true);
            cekFromFastMenu();
            skeletonScreenHistory.show();
            skeletonScreenSaved.show();
        }
    }

    @Override
    public String getTitleBar() {
        return GeneralHelper.getString(R.string.transfer_title_bar);
    }

    public void cekFromFastMenu() {
        if (!isFromFastMenu) {
            binding.llAddSavedList.setOnClickListener(view -> TambahTransferAliasRevampActivity.launchIntent(
                    getActivity(),
                    GeneralHelper.getString(R.string.title_tambah_daftar_tersimpan),
                    GeneralHelper.getString(R.string.url_add_saved_list_inquiry_v3),
                    GeneralHelper.getString(R.string.url_add_saved_list_v3),
                    bankLists,
                    setParameter(),
                    JOURNEY_TYPE_REVAMP_ADD_SAVED_LIST
            ));

//            disableButtonSubmit(false);
//            binding.btnSubmit.setOnClickListener(view -> TambahTransferAliasRevampActivity.launchIntent(
//                    getActivity(),
//                    getTitleBar(),
//                    GeneralHelper.getString(R.string.url_inq_transfer),
//                    GeneralHelper.getString(R.string.url_confirm_transfer),
//                    GeneralHelper.getString(R.string.url_pay_transfer),
//                    GeneralHelper.getString(R.string.url_pay_transfer),
//                    bankLists,
//                    setParameter(),
//                    mJourneyType,
//                    mIsCreateFromBanner
//            ));
        } else {
            binding.llAddSavedList.setVisibility(View.GONE);
//            disableButtonSubmit(true);
        }
    }

    public ParameterModel setParameter() {
        ParameterModel parameterModel = new ParameterModel();

        parameterModel.setStringLabelTujuan(GeneralHelper.getString(R.string.destination_number_hint));
        parameterModel.setStringLabelNominal(GeneralHelper.getString(R.string.transfer_nominal));
        parameterModel.setStringButtonSubmit(GeneralHelper.getString(R.string.transfer));
        parameterModel.setStringLabelMinimum(GeneralHelper.getString(R.string.transfer));
        parameterModel.setDefaultIcon(0);

        return parameterModel;
    }

    protected void disableButtonSubmit(boolean disable) {
//        if (disable) {
//            binding.btnSubmit.setEnabled(false);
//            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60));
//        } else {
//            binding.btnSubmit.setEnabled(true);
//            binding.btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light10));
//        }
    }


    @Override
    public void onSuccessGetRestResponse(RestResponse restResponse) {
        DataListBankResponse dataListBankResponse = restResponse.getData(DataListBankResponse.class);
        bankLists = dataListBankResponse.getBankList();
        mAftBanner = dataListBankResponse.getAftBanner();
        setupBanner();
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void onSuccessGetSavedForm(List<SavedResponse> savedResponse) {
        savedResponses.clear();
        savedResponses.addAll(savedResponse);
        savedAdapter.setSavedResponses(savedResponses);
        savedAdapter.notifyDataSetChanged();

        if (savedResponses.size() > 0) {
            binding.rvDaftarBriva.setVisibility(View.VISIBLE);
            binding.llNoDataSaved.setVisibility(View.GONE);
        } else {
            binding.rvDaftarBriva.setVisibility(View.GONE);
            binding.llNoDataSaved.setVisibility(View.VISIBLE);
        }
    }


    public void initiateHistoryAdapter() {
//        binding.rvBrivaTerakhir.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false));
//        historyAdapter = new HistoryAdapterRevamp(getContext(), historyResponses, this, 0, isFromFastMenu);
//        skeletonScreenHistory = Skeleton.bind(binding.rvBrivaTerakhir)
//                .adapter(historyAdapter)
//                .shimmer(true)
//                .angle(20)
//                .frozen(false)
//                .duration(1200)
//                .count(5)
//                .load(R.layout.item_skeleton_history_revamp)
//                .show();

    }

    @Override
    public void onSuccessGetInquiry(InquiryTransferRevampResponse inquiryTransferRevampResponse) {
        InquiryTransferRevampActivity.launchIntent(getActivity(), inquiryTransferRevampResponse, isFromFastMenu, setParameter(), false, mJourneyType, mIsCreateFromBanner);
    }

    @Override
    public void onSuccessUpdate(SavedResponse savedResponse, int item, int type) {
        String message = GeneralHelper.getString(R.array.type_option_desc_revamp, type);
        showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, getActivity(), true);
        presenter.getDataForm();
    }



    @Override
    public void onShowBubbleFirstTimeVisit() {
        try {
            if (!isFromFastMenu) {
                showBubbleFirstTimeVisit = new BubbleShowCaseBuilder(getActivity())
                        .title(GeneralHelper.getString(R.string.title_bubble_first_time_visit))
                        .description(GeneralHelper.getString(R.string.desc_bubble_first_time_visit))
                        .buttonTitle(GeneralHelper.getString(R.string.mengerti))
                        .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
                        .backgroundColor(Color.WHITE)
                        .textColor(Color.BLACK)
                        .titleTextSize(18)
                        .descriptionTextSize(14)
                        .targetView(binding.llAddSavedList);
                showBubbleFirstTimeVisit.show();
            }
        } catch (Exception e) {

        }
    }

    public void initiateSavedAdapter() {
        binding.rvDaftarBriva.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.VERTICAL, false));
        if (getContext() != null)
            savedAdapter = new SavedAdapterRevamp(getContext(), savedResponses, this, 0, isFromFastMenu);

        skeletonScreenSaved = Skeleton.bind(binding.rvDaftarBriva)
                .adapter(savedAdapter)
                .shimmer(true)
                .angle(20)
                .frozen(false)
                .duration(1200)
                .count(5)
                .load(R.layout.item_skeleton_saved_transfer)
                .show();
    }

    @Override
    public void onClickHistoryItem(HistoryResponse historyResponse) {
        String s = historyResponse.getValue();
        String[] strArr = s.split("\\|");
        if (strArr.length > 1) {
            String bankCode = strArr[0];
            String accountStr = strArr[1];
            presenter.getDataInquiry(bankCode, accountStr, isFromFastMenu);
        }

    }

    @Override
    public void onClickSavedItem(@NonNull SavedResponse savedResponse) {
        String s = savedResponse.getValue();
        String[] strArr = s.split("\\|");
        if (strArr.length > 2) {
            String bankCode = strArr[1];
            String accountStr = strArr[2];
            presenter.getDataInquiry(bankCode, accountStr, isFromFastMenu);
        }
    }

    @Override
    public void onClickUpdateItem(@NonNull SavedResponse lSavedResponse, int position) {
        if (getActivity() != null) {
            SearchRevampFragment SearchRevampFragment = new SearchRevampFragment(fetchList(lSavedResponse), optionSearchRevampModel -> {
                if (optionSearchRevampModel.getCodeModel().equals(String.valueOf(Constant.EditOption.EDIT))) {
                    onEditSavedList(lSavedResponse, position);
                } else if (optionSearchRevampModel.getCodeModel().equals(String.valueOf(Constant.EditOption.HAPUS))) {
                    onDeleteSavedList(lSavedResponse, position);
                } else if (optionSearchRevampModel.getCodeModel().equals(String.valueOf(Constant.EditOption.NON_FAV))) {
                    presenter.setUpdateItemTf(GeneralHelper.getString(R.string.url_unfavorit_transfer_v3), lSavedResponse, position, Constant.EditOption.NON_FAV);
                } else if (optionSearchRevampModel.getCodeModel().equals(String.valueOf(Constant.EditOption.FAV))) {
                    presenter.setUpdateItemTf(GeneralHelper.getString(R.string.url_favorit_transfer_v3), lSavedResponse, position, Constant.EditOption.FAV);
                }
            }, "", GeneralHelper.getString(R.string.setting), false);
            SearchRevampFragment.show(this.getActivity().getSupportFragmentManager(), "");
        }
    }

    private void onEditSavedList(SavedResponse lSavedResponse, int position) {
        FormEditSavedRevampActivity.launchIntent(getActivity(), lSavedResponse, position, 0, GeneralHelper.getString(R.string.url_update_transfer_v2), GeneralHelper.getString(R.string.destination_number_hint));
    }

    private void onDeleteSavedList(SavedResponse lSavedResponse, int position) {
        String dialogTitle = GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_title);
        String dialogDesc = String.format(GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_desc), lSavedResponse.getTitle());
        DialogSetDefaultRevamp dialogSetDefaultRevamp = new DialogSetDefaultRevamp(new DialogSetDefaultRevamp.DialogDefaultListener() {
            @Override
            public void onClickYesDefault(int requestId) {
                presenter.setUpdateItemTf(GeneralHelper.getString(R.string.url_delete_transfer_v3), lSavedResponse, position, Constant.EditOption.HAPUS);
            }

            @Override
            public void onClickNoDefault(int requestId) {

            }
        }, dialogTitle, dialogDesc, GeneralHelper.getString(R.string.hapus), GeneralHelper.getString(R.string.batal), Constant.REQ_EDIT_SAVED);
        if (getActivity() == null) return;
        FragmentTransaction ft = getActivity().getSupportFragmentManager().beginTransaction();
        ft.add(dialogSetDefaultRevamp, null);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void onNoSavedItemSearch(boolean isTrue, CharSequence query) {
        if (isTrue) {
            final StringBuilder sb = new StringBuilder(query.length());
            sb.append(query);
            if (query.length() != 0) {
                String desc = String.format(GeneralHelper.getString(R.string.no_data_saved_list_subtitle), "\"" + sb + "\"");
//                binding.tvNoDataFoundDesc.setText(desc);
                binding.rvDaftarBriva.setVisibility(View.GONE);
//                binding.llNoDataSavedFound.setVisibility(View.VISIBLE);
                if (savedResponses.size() == 0) {
//                    binding.llNoDataSavedFound.setVisibility(View.GONE);
                }
            } else {
//                binding.llNoDataSavedFound.setVisibility(View.GONE);
                binding.rvDaftarBriva.setVisibility(View.VISIBLE);
            }
        } else {
//            binding.llNoDataSavedFound.setVisibility(View.GONE);
            binding.rvDaftarBriva.setVisibility(View.VISIBLE);
        }
    }

    private List<OptionSearchRevampModel> fetchList(SavedResponse savedResponse) {
        List<OptionSearchRevampModel> optionSearchRevampModelArrayList = new ArrayList<>();
        if (getActivity() != null) {
            if (Boolean.TRUE.equals(savedResponse.getFavorite())) {
                optionSearchRevampModelArrayList.add(new OptionSearchRevampModel("ic_opt_fav", GeneralHelper.getString(getActivity(), R.string.opt_non_fav), "", 0, 0, String.valueOf(Constant.EditOption.NON_FAV), true));
            } else {
                optionSearchRevampModelArrayList.add(new OptionSearchRevampModel("ic_opt_fav", GeneralHelper.getString(getActivity(), R.string.opt_fav), "", 0, 0, String.valueOf(Constant.EditOption.FAV), true));
            }
            optionSearchRevampModelArrayList.add(new OptionSearchRevampModel("ic_opt_edit", GeneralHelper.getString(getActivity(), R.string.edit_daftar_transfer), "", 0, 0, String.valueOf(Constant.EditOption.EDIT), true));
            optionSearchRevampModelArrayList.add(new OptionSearchRevampModel("ic_opt_hapus", GeneralHelper.getString(getActivity(), R.string.unfavorit_saved), "", 0, 0, String.valueOf(Constant.EditOption.HAPUS), true));
        }

        return optionSearchRevampModelArrayList;
    }


    @Override
    public void onSessionEnd(String message) {
        if (getActivity() != null) {
            ((FormTransferAliasRevampActivity) getActivity()).onSessionEnd(message);
        }
        presenter.stop();
    }

    @Override
    public void onException(String message) {
        try {
            if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
                GeneralHelper.showDialogGagalBack(getActivity(), message);
            } else if (getActivity() != null) {
                GeneralHelper.showSnackBarRevamp(requireActivity().findViewById(R.id.content), message);
            }
        } catch (Exception e) {
            // donothing
        }
    }
}