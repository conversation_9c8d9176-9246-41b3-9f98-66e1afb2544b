<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/item_no_data_transaction"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginLeft="@dimen/space_x3_half"
    android:layout_marginTop="@dimen/space_x2_half"
    android:layout_marginRight="@dimen/space_x3_half"
    android:layout_marginBottom="@dimen/space_x1_half"
    android:gravity="center"
    android:minWidth="44dp"
    android:minHeight="@dimen/space_x12"
    android:orientation="horizontal">

    <ImageView
        android:layout_width="@dimen/space_x7"
        android:layout_height="@dimen/space_x7"
        android:layout_gravity="center"
        android:layout_marginEnd="@dimen/space_x1"
        android:src="@drawable/belum_ada" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            style="@style/BodyMediumText.Bold.Black"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/there_is_no_transaction" />

        <TextView
            android:id="@+id/tv_belum_ada_transaksi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1"
            android:text="@string/belum_ada_transaksi" />
    </LinearLayout>

</LinearLayout>
